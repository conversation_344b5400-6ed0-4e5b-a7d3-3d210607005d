<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .header-text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: white; }
      .base-agent { fill: #95a5a6; stroke: #7f8c8d; stroke-width: 2; rx: 8; }
      .react-agent { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .planning-agent { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .executor-agent { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 8; }
      .reactimpl-agent { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .summary-agent { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; rx: 8; }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">JoyAgent-JDGenie Agent类型详细对比</text>
  
  <!-- Agent类型枚举 -->
  <rect x="50" y="60" width="1500" height="80" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="8"/>
  <text x="800" y="85" text-anchor="middle" class="subtitle" fill="white">Agent类型枚举 (AgentType)</text>
  <text x="70" y="105" class="text" fill="white">1. COMPREHENSIVE(1) - 综合型</text>
  <text x="320" y="105" class="text" fill="white">2. WORKFLOW(2) - 工作流型</text>
  <text x="570" y="105" class="text" fill="white">3. PLAN_SOLVE(3) - 规划解决型</text>
  <text x="820" y="105" class="text" fill="white">4. ROUTER(4) - 路由型</text>
  <text x="1070" y="105" class="text" fill="white">5. REACT(5) - 反应型</text>
  <text x="70" y="125" class="small-text" fill="white">目前主要实现了PLAN_SOLVE(3)和REACT(5)两种类型</text>
  
  <!-- 继承关系图 -->
  <rect x="50" y="160" width="1500" height="200" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="8"/>
  <text x="800" y="180" text-anchor="middle" class="subtitle">Agent继承关系图</text>
  
  <!-- BaseAgent -->
  <rect x="700" y="200" width="200" height="60" class="base-agent"/>
  <text x="800" y="220" text-anchor="middle" class="header-text">BaseAgent</text>
  <text x="710" y="235" class="small-text" fill="white">• 抽象基类</text>
  <text x="710" y="250" class="small-text" fill="white">• 定义基础属性和方法</text>
  
  <!-- ReActAgent -->
  <rect x="400" y="300" width="200" height="60" class="react-agent"/>
  <text x="500" y="320" text-anchor="middle" class="header-text">ReActAgent</text>
  <text x="410" y="335" class="small-text" fill="white">• 抽象ReAct模式</text>
  <text x="410" y="350" class="small-text" fill="white">• think() + act()</text>
  
  <!-- SummaryAgent -->
  <rect x="1000" y="300" width="200" height="60" class="summary-agent"/>
  <text x="1100" y="320" text-anchor="middle" class="header-text">SummaryAgent</text>
  <text x="1010" y="335" class="small-text" fill="white">• 任务总结</text>
  <text x="1010" y="350" class="small-text" fill="white">• 直接继承BaseAgent</text>
  
  <!-- 具体实现 -->
  <rect x="100" y="400" width="150" height="60" class="planning-agent"/>
  <text x="175" y="420" text-anchor="middle" class="header-text">PlanningAgent</text>
  <text x="110" y="435" class="small-text" fill="white">• 任务规划</text>
  <text x="110" y="450" class="small-text" fill="white">• 继承ReActAgent</text>
  
  <rect x="300" y="400" width="150" height="60" class="executor-agent"/>
  <text x="375" y="420" text-anchor="middle" class="header-text">ExecutorAgent</text>
  <text x="310" y="435" class="small-text" fill="white">• 任务执行</text>
  <text x="310" y="450" class="small-text" fill="white">• 继承ReActAgent</text>
  
  <rect x="500" y="400" width="150" height="60" class="reactimpl-agent"/>
  <text x="575" y="420" text-anchor="middle" class="header-text">ReactImplAgent</text>
  <text x="510" y="435" class="small-text" fill="white">• React实现</text>
  <text x="510" y="450" class="small-text" fill="white">• 继承ReActAgent</text>
  
  <!-- 继承箭头 -->
  <line x1="800" y1="260" x2="500" y2="300" class="flow-arrow"/>
  <line x1="800" y1="260" x2="1100" y2="300" class="flow-arrow"/>
  <line x1="500" y1="360" x2="175" y2="400" class="flow-arrow"/>
  <line x1="500" y1="360" x2="375" y2="400" class="flow-arrow"/>
  <line x1="500" y1="360" x2="575" y2="400" class="flow-arrow"/>
  
  <!-- 详细对比表 -->
  <rect x="50" y="480" width="1500" height="680" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="8"/>
  <text x="800" y="505" text-anchor="middle" class="subtitle">Agent详细对比表</text>
  
  <!-- 表头 -->
  <rect x="70" y="520" width="120" height="40" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
  <text x="130" y="545" text-anchor="middle" class="header-text">对比维度</text>
  
  <rect x="190" y="520" width="260" height="40" fill="#e74c3c" stroke="#c0392b" stroke-width="1"/>
  <text x="320" y="545" text-anchor="middle" class="header-text">PlanningAgent (规划型)</text>
  
  <rect x="450" y="520" width="260" height="40" fill="#f39c12" stroke="#e67e22" stroke-width="1"/>
  <text x="580" y="545" text-anchor="middle" class="header-text">ExecutorAgent (执行型)</text>
  
  <rect x="710" y="520" width="260" height="40" fill="#2ecc71" stroke="#27ae60" stroke-width="1"/>
  <text x="840" y="545" text-anchor="middle" class="header-text">ReactImplAgent (反应型)</text>
  
  <rect x="970" y="520" width="260" height="40" fill="#9b59b6" stroke="#8e44ad" stroke-width="1"/>
  <text x="1100" y="545" text-anchor="middle" class="header-text">SummaryAgent (总结型)</text>
  
  <rect x="1230" y="520" width="260" height="40" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
  <text x="1360" y="545" text-anchor="middle" class="header-text">ReActAgent (抽象基类)</text>
  
  <!-- 行1: AgentType -->
  <rect x="70" y="560" width="120" height="40" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="130" y="585" text-anchor="middle" class="text">AgentType</text>
  
  <rect x="190" y="560" width="260" height="40" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="320" y="585" text-anchor="middle" class="text">PLAN_SOLVE (3)</text>
  
  <rect x="450" y="560" width="260" height="40" fill="#fdeaa7" stroke="#f39c12" stroke-width="1"/>
  <text x="580" y="585" text-anchor="middle" class="text">PLAN_SOLVE (3)</text>
  
  <rect x="710" y="560" width="260" height="40" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="840" y="585" text-anchor="middle" class="text">REACT (5)</text>
  
  <rect x="970" y="560" width="260" height="40" fill="#e8daef" stroke="#9b59b6" stroke-width="1"/>
  <text x="1100" y="585" text-anchor="middle" class="text">辅助Agent</text>
  
  <rect x="1230" y="560" width="260" height="40" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="1360" y="585" text-anchor="middle" class="text">抽象类</text>
  
  <!-- 行2: 主要职责 -->
  <rect x="70" y="600" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="130" y="620" text-anchor="middle" class="text">主要职责</text>
  <text x="130" y="635" text-anchor="middle" class="text">和用途</text>
  
  <rect x="190" y="600" width="260" height="60" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="320" y="615" text-anchor="middle" class="small-text">• 任务规划和分解</text>
  <text x="320" y="630" text-anchor="middle" class="small-text">• 创建执行计划</text>
  <text x="320" y="645" text-anchor="middle" class="small-text">• 管理任务状态</text>
  <text x="320" y="655" text-anchor="middle" class="small-text">• 调度ExecutorAgent</text>
  
  <rect x="450" y="600" width="260" height="60" fill="#fdeaa7" stroke="#f39c12" stroke-width="1"/>
  <text x="580" y="615" text-anchor="middle" class="small-text">• 执行具体任务</text>
  <text x="580" y="630" text-anchor="middle" class="small-text">• 调用工具完成子任务</text>
  <text x="580" y="645" text-anchor="middle" class="small-text">• 处理工具结果</text>
  <text x="580" y="655" text-anchor="middle" class="small-text">• 与PlanningAgent协作</text>
  
  <rect x="710" y="600" width="260" height="60" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="840" y="615" text-anchor="middle" class="small-text">• 独立完成整个任务</text>
  <text x="840" y="630" text-anchor="middle" class="small-text">• 直接响应用户查询</text>
  <text x="840" y="645" text-anchor="middle" class="small-text">• 简单快速的执行模式</text>
  <text x="840" y="655" text-anchor="middle" class="small-text">• 适合单步骤任务</text>
  
  <rect x="970" y="600" width="260" height="60" fill="#e8daef" stroke="#9b59b6" stroke-width="1"/>
  <text x="1100" y="615" text-anchor="middle" class="small-text">• 总结任务执行结果</text>
  <text x="1100" y="630" text-anchor="middle" class="small-text">• 提取关键信息</text>
  <text x="1100" y="645" text-anchor="middle" class="small-text">• 格式化输出</text>
  <text x="1100" y="655" text-anchor="middle" class="small-text">• 生成最终报告</text>
  
  <rect x="1230" y="600" width="260" height="60" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="1360" y="615" text-anchor="middle" class="small-text">• 定义ReAct模式</text>
  <text x="1360" y="630" text-anchor="middle" class="small-text">• think() + act() 循环</text>
  <text x="1360" y="645" text-anchor="middle" class="small-text">• 抽象方法定义</text>
  <text x="1360" y="655" text-anchor="middle" class="small-text">• 为子类提供框架</text>

  <!-- 行3: 工具集合 -->
  <rect x="70" y="660" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="130" y="680" text-anchor="middle" class="text">可用工具</text>
  <text x="130" y="695" text-anchor="middle" class="text">集合</text>

  <rect x="190" y="660" width="260" height="60" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="320" y="675" text-anchor="middle" class="small-text">• planning工具 (核心)</text>
  <text x="320" y="690" text-anchor="middle" class="small-text">• 任务管理相关工具</text>
  <text x="320" y="705" text-anchor="middle" class="small-text">• 计划创建/更新/标记</text>
  <text x="320" y="715" text-anchor="middle" class="small-text">• finish命令</text>

  <rect x="450" y="660" width="260" height="60" fill="#fdeaa7" stroke="#f39c12" stroke-width="1"/>
  <text x="580" y="675" text-anchor="middle" class="small-text">• 全套功能工具</text>
  <text x="580" y="690" text-anchor="middle" class="small-text">• search, code, report</text>
  <text x="580" y="705" text-anchor="middle" class="small-text">• file_tool, deep_search</text>
  <text x="580" y="715" text-anchor="middle" class="small-text">• MCP外部工具</text>

  <rect x="710" y="660" width="260" height="60" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="840" y="675" text-anchor="middle" class="small-text">• 全套功能工具</text>
  <text x="840" y="690" text-anchor="middle" class="small-text">• search, code, report</text>
  <text x="840" y="705" text-anchor="middle" class="small-text">• file_tool, deep_search</text>
  <text x="840" y="715" text-anchor="middle" class="small-text">• MCP外部工具</text>

  <rect x="970" y="660" width="260" height="60" fill="#e8daef" stroke="#9b59b6" stroke-width="1"/>
  <text x="1100" y="675" text-anchor="middle" class="small-text">• 无工具调用</text>
  <text x="1100" y="690" text-anchor="middle" class="small-text">• 纯LLM总结</text>
  <text x="1100" y="705" text-anchor="middle" class="small-text">• 基于Memory分析</text>
  <text x="1100" y="715" text-anchor="middle" class="small-text">• 文本处理</text>

  <rect x="1230" y="660" width="260" height="60" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="1360" y="675" text-anchor="middle" class="small-text">• 由子类决定</text>
  <text x="1360" y="690" text-anchor="middle" class="small-text">• 提供工具管理框架</text>
  <text x="1360" y="705" text-anchor="middle" class="small-text">• ToolCollection</text>
  <text x="1360" y="715" text-anchor="middle" class="small-text">• executeTool方法</text>

  <!-- 行4: 执行模式 -->
  <rect x="70" y="720" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="130" y="740" text-anchor="middle" class="text">执行模式</text>
  <text x="130" y="755" text-anchor="middle" class="text">和流程</text>

  <rect x="190" y="720" width="260" height="60" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="320" y="735" text-anchor="middle" class="small-text">• 规划驱动模式</text>
  <text x="320" y="750" text-anchor="middle" class="small-text">• 先规划后执行</text>
  <text x="320" y="765" text-anchor="middle" class="small-text">• 与ExecutorAgent协作</text>
  <text x="320" y="775" text-anchor="middle" class="small-text">• 多轮迭代优化</text>

  <rect x="450" y="720" width="260" height="60" fill="#fdeaa7" stroke="#f39c12" stroke-width="1"/>
  <text x="580" y="735" text-anchor="middle" class="small-text">• 任务执行模式</text>
  <text x="580" y="750" text-anchor="middle" class="small-text">• 接收具体任务</text>
  <text x="580" y="765" text-anchor="middle" class="small-text">• 调用工具完成</text>
  <text x="580" y="775" text-anchor="middle" class="small-text">• 返回执行结果</text>

  <rect x="710" y="720" width="260" height="60" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="840" y="735" text-anchor="middle" class="small-text">• 直接响应模式</text>
  <text x="840" y="750" text-anchor="middle" class="small-text">• 端到端处理</text>
  <text x="840" y="765" text-anchor="middle" class="small-text">• 自主决策工具使用</text>
  <text x="840" y="775" text-anchor="middle" class="small-text">• 单Agent完成</text>

  <rect x="970" y="720" width="260" height="60" fill="#e8daef" stroke="#9b59b6" stroke-width="1"/>
  <text x="1100" y="735" text-anchor="middle" class="small-text">• 后处理模式</text>
  <text x="1100" y="750" text-anchor="middle" class="small-text">• 分析执行历史</text>
  <text x="1100" y="765" text-anchor="middle" class="small-text">• 提取关键信息</text>
  <text x="1100" y="775" text-anchor="middle" class="small-text">• 生成总结报告</text>

  <rect x="1230" y="720" width="260" height="60" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="1360" y="735" text-anchor="middle" class="small-text">• ReAct循环模式</text>
  <text x="1360" y="750" text-anchor="middle" class="small-text">• think() → act()</text>
  <text x="1360" y="765" text-anchor="middle" class="small-text">• 推理-行动循环</text>
  <text x="1360" y="775" text-anchor="middle" class="small-text">• 步骤化执行</text>

  <!-- 行5: 系统提示词 -->
  <rect x="70" y="780" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="130" y="800" text-anchor="middle" class="text">系统提示词</text>
  <text x="130" y="815" text-anchor="middle" class="text">特点</text>

  <rect x="190" y="780" width="260" height="60" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="320" y="795" text-anchor="middle" class="small-text">• 规划专用prompt</text>
  <text x="320" y="810" text-anchor="middle" class="small-text">• 任务分解指导</text>
  <text x="320" y="825" text-anchor="middle" class="small-text">• planning工具说明</text>
  <text x="320" y="835" text-anchor="middle" class="small-text">• SOP流程指导</text>

  <rect x="450" y="780" width="260" height="60" fill="#fdeaa7" stroke="#f39c12" stroke-width="1"/>
  <text x="580" y="795" text-anchor="middle" class="small-text">• 执行专用prompt</text>
  <text x="580" y="810" text-anchor="middle" class="small-text">• 工具使用指导</text>
  <text x="580" y="825" text-anchor="middle" class="small-text">• 任务完成判断</text>
  <text x="580" y="835" text-anchor="middle" class="small-text">• 结果处理说明</text>

  <rect x="710" y="780" width="260" height="60" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="840" y="795" text-anchor="middle" class="small-text">• React专用prompt</text>
  <text x="840" y="810" text-anchor="middle" class="small-text">• 思考-行动指导</text>
  <text x="840" y="825" text-anchor="middle" class="small-text">• 工具选择策略</text>
  <text x="840" y="835" text-anchor="middle" class="small-text">• 完成条件判断</text>

  <rect x="970" y="780" width="260" height="60" fill="#e8daef" stroke="#9b59b6" stroke-width="1"/>
  <text x="1100" y="795" text-anchor="middle" class="small-text">• 总结专用prompt</text>
  <text x="1100" y="810" text-anchor="middle" class="small-text">• 信息提取指导</text>
  <text x="1100" y="825" text-anchor="middle" class="small-text">• 格式化要求</text>
  <text x="1100" y="835" text-anchor="middle" class="small-text">• 关键点识别</text>

  <rect x="1230" y="780" width="260" height="60" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="1360" y="795" text-anchor="middle" class="small-text">• 基础框架prompt</text>
  <text x="1360" y="810" text-anchor="middle" class="small-text">• 通用指导原则</text>
  <text x="1360" y="825" text-anchor="middle" class="small-text">• 子类可定制</text>
  <text x="1360" y="835" text-anchor="middle" class="small-text">• 模板化设计</text>

  <!-- 行6: 使用场景 -->
  <rect x="70" y="840" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="130" y="860" text-anchor="middle" class="text">适用场景</text>
  <text x="130" y="875" text-anchor="middle" class="text">和优势</text>

  <rect x="190" y="840" width="260" height="60" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="320" y="855" text-anchor="middle" class="small-text">• 复杂多步骤任务</text>
  <text x="320" y="870" text-anchor="middle" class="small-text">• 需要详细规划</text>
  <text x="320" y="885" text-anchor="middle" class="small-text">• 任务可分解</text>
  <text x="320" y="895" text-anchor="middle" class="small-text">• 高质量输出</text>

  <rect x="450" y="840" width="260" height="60" fill="#fdeaa7" stroke="#f39c12" stroke-width="1"/>
  <text x="580" y="855" text-anchor="middle" class="small-text">• 具体任务执行</text>
  <text x="580" y="870" text-anchor="middle" class="small-text">• 工具密集型任务</text>
  <text x="580" y="885" text-anchor="middle" class="small-text">• 与规划Agent协作</text>
  <text x="580" y="895" text-anchor="middle" class="small-text">• 专业化执行</text>

  <rect x="710" y="840" width="260" height="60" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="840" y="855" text-anchor="middle" class="small-text">• 简单直接任务</text>
  <text x="840" y="870" text-anchor="middle" class="small-text">• 快速响应需求</text>
  <text x="840" y="885" text-anchor="middle" class="small-text">• 单轮对话</text>
  <text x="840" y="895" text-anchor="middle" class="small-text">• 轻量级处理</text>

  <rect x="970" y="840" width="260" height="60" fill="#e8daef" stroke="#9b59b6" stroke-width="1"/>
  <text x="1100" y="855" text-anchor="middle" class="small-text">• 结果整理</text>
  <text x="1100" y="870" text-anchor="middle" class="small-text">• 报告生成</text>
  <text x="1100" y="885" text-anchor="middle" class="small-text">• 信息汇总</text>
  <text x="1100" y="895" text-anchor="middle" class="small-text">• 后处理优化</text>

  <rect x="1230" y="840" width="260" height="60" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="1360" y="855" text-anchor="middle" class="small-text">• 框架设计</text>
  <text x="1360" y="870" text-anchor="middle" class="small-text">• 模式定义</text>
  <text x="1360" y="885" text-anchor="middle" class="small-text">• 代码复用</text>
  <text x="1360" y="895" text-anchor="middle" class="small-text">• 扩展基础</text>

  <!-- 行7: Handler映射 -->
  <rect x="70" y="900" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="130" y="920" text-anchor="middle" class="text">Handler</text>
  <text x="130" y="935" text-anchor="middle" class="text">映射</text>

  <rect x="190" y="900" width="260" height="60" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="320" y="915" text-anchor="middle" class="small-text">PlanSolveHandlerImpl</text>
  <text x="320" y="930" text-anchor="middle" class="small-text">• 协调Planning+Executor</text>
  <text x="320" y="945" text-anchor="middle" class="small-text">• 多Agent协作</text>
  <text x="320" y="955" text-anchor="middle" class="small-text">• 复杂流程控制</text>

  <rect x="450" y="900" width="260" height="60" fill="#fdeaa7" stroke="#f39c12" stroke-width="1"/>
  <text x="580" y="915" text-anchor="middle" class="small-text">PlanSolveHandlerImpl</text>
  <text x="580" y="930" text-anchor="middle" class="small-text">• 与PlanningAgent协作</text>
  <text x="580" y="945" text-anchor="middle" class="small-text">• 任务执行部分</text>
  <text x="580" y="955" text-anchor="middle" class="small-text">• 工具调用处理</text>

  <rect x="710" y="900" width="260" height="60" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="840" y="915" text-anchor="middle" class="small-text">ReactHandlerImpl</text>
  <text x="840" y="930" text-anchor="middle" class="small-text">• 独立Agent处理</text>
  <text x="840" y="945" text-anchor="middle" class="small-text">• 单Agent模式</text>
  <text x="840" y="955" text-anchor="middle" class="small-text">• 直接响应处理</text>

  <rect x="970" y="900" width="260" height="60" fill="#e8daef" stroke="#9b59b6" stroke-width="1"/>
  <text x="1100" y="915" text-anchor="middle" class="small-text">内部调用</text>
  <text x="1100" y="930" text-anchor="middle" class="small-text">• 由其他Handler调用</text>
  <text x="1100" y="945" text-anchor="middle" class="small-text">• 后处理组件</text>
  <text x="1100" y="955" text-anchor="middle" class="small-text">• 辅助功能</text>

  <rect x="1230" y="900" width="260" height="60" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="1360" y="915" text-anchor="middle" class="small-text">无直接Handler</text>
  <text x="1360" y="930" text-anchor="middle" class="small-text">• 抽象基类</text>
  <text x="1360" y="945" text-anchor="middle" class="small-text">• 不直接实例化</text>
  <text x="1360" y="955" text-anchor="middle" class="small-text">• 为子类服务</text>

  <!-- 总结 -->
  <rect x="70" y="980" width="1420" height="80" fill="#2c3e50" stroke="#34495e" stroke-width="2" rx="8"/>
  <text x="780" y="1005" text-anchor="middle" class="subtitle" fill="white">核心区别总结</text>
  <text x="90" y="1025" class="text" fill="white">• PLAN_SOLVE模式：PlanningAgent负责规划，ExecutorAgent负责执行，适合复杂多步骤任务</text>
  <text x="90" y="1040" class="text" fill="white">• REACT模式：ReactImplAgent独立完成整个任务，适合简单直接的查询响应</text>
  <text x="90" y="1055" class="text" fill="white">• 两种模式都会调用SummaryAgent进行最终结果总结和格式化输出</text>
</svg>
