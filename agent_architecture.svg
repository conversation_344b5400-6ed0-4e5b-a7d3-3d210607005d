<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .agent-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .component-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 6; }
      .tool-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 6; }
      .context-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 6; }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">JoyAgent-JDGenie 多智能体系统架构</text>
  
  <!-- Agent类型枚举 -->
  <rect x="50" y="60" width="200" height="120" class="agent-box"/>
  <text x="150" y="80" text-anchor="middle" class="subtitle" fill="white">Agent类型</text>
  <text x="60" y="100" class="text" fill="white">1. COMPREHENSIVE</text>
  <text x="60" y="115" class="text" fill="white">2. WORKFLOW</text>
  <text x="60" y="130" class="text" fill="white">3. PLAN_SOLVE</text>
  <text x="60" y="145" class="text" fill="white">4. ROUTER</text>
  <text x="60" y="160" class="text" fill="white">5. REACT (主要)</text>
  
  <!-- 核心Agent架构 -->
  <rect x="300" y="60" width="250" height="180" class="agent-box"/>
  <text x="425" y="80" text-anchor="middle" class="subtitle" fill="white">BaseAgent 基类</text>
  <text x="310" y="100" class="text" fill="white">• Memory (消息历史)</text>
  <text x="310" y="115" class="text" fill="white">• ToolCollection (工具集合)</text>
  <text x="310" y="130" class="text" fill="white">• LLM (语言模型)</text>
  <text x="310" y="145" class="text" fill="white">• AgentContext (上下文)</text>
  <text x="310" y="160" class="text" fill="white">• State: IDLE/RUNNING/FINISHED/ERROR</text>
  <text x="310" y="175" class="text" fill="white">• maxSteps: 10-40步</text>
  <text x="310" y="190" class="text" fill="white">• currentStep: 当前步骤</text>
  <text x="310" y="205" class="text" fill="white">• duplicateThreshold: 重复阈值</text>
  <text x="310" y="220" class="text" fill="white">• Printer: SSE输出</text>
  
  <!-- ReAct Agent -->
  <rect x="600" y="60" width="200" height="120" class="agent-box"/>
  <text x="700" y="80" text-anchor="middle" class="subtitle" fill="white">ReActAgent</text>
  <text x="610" y="100" class="text" fill="white">think() → 思考</text>
  <text x="610" y="115" class="text" fill="white">act() → 执行</text>
  <text x="610" y="130" class="text" fill="white">step() → 单步执行</text>
  <text x="610" y="145" class="text" fill="white">run() → 主循环</text>
  <text x="610" y="160" class="text" fill="white">generateDigitalEmployee()</text>
  
  <!-- Function Call机制 -->
  <rect x="850" y="60" width="300" height="180" class="component-box"/>
  <text x="1000" y="80" text-anchor="middle" class="subtitle" fill="white">Function Call机制</text>
  <text x="860" y="100" class="text" fill="white">1. LLM.askTool() 调用</text>
  <text x="860" y="115" class="text" fill="white">2. ToolChoice.AUTO 自动选择</text>
  <text x="860" y="130" class="text" fill="white">3. 支持两种模式:</text>
  <text x="870" y="145" class="text" fill="white">• function_call (标准)</text>
  <text x="870" y="160" class="text" fill="white">• struct_parse (JSON解析)</text>
  <text x="860" y="175" class="text" fill="white">4. ToolCall解析与执行</text>
  <text x="860" y="190" class="text" fill="white">5. 结果添加到Memory</text>
  <text x="860" y="205" class="text" fill="white">6. 流式/非流式响应</text>
  <text x="860" y="220" class="text" fill="white">7. 超时控制: 300s</text>
  
  <!-- 工具系统 -->
  <rect x="50" y="280" width="300" height="150" class="tool-box"/>
  <text x="200" y="300" text-anchor="middle" class="subtitle" fill="white">工具系统</text>
  <text x="60" y="320" class="text" fill="white">1. 代码执行工具 (CodeInterpreterTool)</text>
  <text x="70" y="335" class="text" fill="white">• 调用外部代码执行服务</text>
  <text x="70" y="350" class="text" fill="white">• 支持流式响应</text>
  <text x="60" y="370" class="text" fill="white">2. MCP工具 (McpTool)</text>
  <text x="70" y="385" class="text" fill="white">• 模型控制协议工具</text>
  <text x="70" y="400" class="text" fill="white">• 通过MCP客户端调用</text>
  <text x="60" y="420" class="text" fill="white">3. 其他自定义工具</text>
  
  <!-- 网络搜索系统 -->
  <rect x="400" y="280" width="350" height="150" class="tool-box"/>
  <text x="575" y="300" text-anchor="middle" class="subtitle" fill="white">网络搜索系统</text>
  <text x="410" y="320" class="text" fill="white">1. 多引擎搜索 (MixSearch)</text>
  <text x="420" y="335" class="text" fill="white">• BingSearch: 必应搜索</text>
  <text x="420" y="350" class="text" fill="white">• JinaSearch: Jina搜索</text>
  <text x="420" y="365" class="text" fill="white">• SogouSearch: 搜狗搜索</text>
  <text x="420" y="380" class="text" fill="white">• SerpSearch: SERP搜索</text>
  <text x="410" y="400" class="text" fill="white">2. 深度搜索 (DeepSearch)</text>
  <text x="420" y="415" class="text" fill="white">• 并发搜索多个引擎</text>
  
  <!-- 上下文管理 -->
  <rect x="800" y="280" width="350" height="150" class="context-box"/>
  <text x="975" y="300" text-anchor="middle" class="subtitle" fill="white">上下文管理</text>
  <text x="810" y="320" class="text" fill="white">1. Memory类 - 消息历史管理</text>
  <text x="820" y="335" class="text" fill="white">• addMessage() 添加消息</text>
  <text x="820" y="350" class="text" fill="white">• clearToolContext() 清理工具上下文</text>
  <text x="810" y="370" class="text" fill="white">2. 长上下文处理</text>
  <text x="820" y="385" class="text" fill="white">• truncateMessage() Token截断</text>
  <text x="820" y="400" class="text" fill="white">• maxInputTokens 限制</text>
  <text x="820" y="415" class="text" fill="white">• 保持系统消息和最新对话</text>
  
  <!-- 执行流程 -->
  <rect x="50" y="480" width="1100" height="200" class="component-box"/>
  <text x="600" y="500" text-anchor="middle" class="subtitle" fill="white">Agent执行流程</text>
  
  <!-- 流程步骤 -->
  <rect x="80" y="520" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="140" y="540" text-anchor="middle" class="text">1. 用户请求</text>
  <text x="140" y="555" text-anchor="middle" class="small-text">GenieController</text>
  <text x="140" y="570" text-anchor="middle" class="small-text">接收请求</text>
  
  <rect x="230" y="520" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="290" y="540" text-anchor="middle" class="text">2. Agent选择</text>
  <text x="290" y="555" text-anchor="middle" class="small-text">AgentHandlerFactory</text>
  <text x="290" y="570" text-anchor="middle" class="small-text">路由到对应Handler</text>
  
  <rect x="380" y="520" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="440" y="540" text-anchor="middle" class="text">3. Think阶段</text>
  <text x="440" y="555" text-anchor="middle" class="small-text">LLM.askTool()</text>
  <text x="440" y="570" text-anchor="middle" class="small-text">分析并选择工具</text>
  
  <rect x="530" y="520" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="590" y="540" text-anchor="middle" class="text">4. Act阶段</text>
  <text x="590" y="555" text-anchor="middle" class="small-text">executeTool()</text>
  <text x="590" y="570" text-anchor="middle" class="small-text">执行选中工具</text>
  
  <rect x="680" y="520" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="740" y="540" text-anchor="middle" class="text">5. 结果处理</text>
  <text x="740" y="555" text-anchor="middle" class="small-text">updateMemory()</text>
  <text x="740" y="570" text-anchor="middle" class="small-text">更新上下文</text>
  
  <rect x="830" y="520" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="890" y="540" text-anchor="middle" class="text">6. 循环判断</text>
  <text x="890" y="555" text-anchor="middle" class="small-text">maxSteps检查</text>
  <text x="890" y="570" text-anchor="middle" class="small-text">状态判断</text>
  
  <rect x="980" y="520" width="120" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="1040" y="540" text-anchor="middle" class="text">7. 结果输出</text>
  <text x="1040" y="555" text-anchor="middle" class="small-text">SummaryAgent</text>
  <text x="1040" y="570" text-anchor="middle" class="small-text">SSE流式输出</text>
  
  <!-- 流程箭头 -->
  <line x1="200" y1="550" x2="230" y2="550" class="flow-arrow"/>
  <line x1="350" y1="550" x2="380" y2="550" class="flow-arrow"/>
  <line x1="500" y1="550" x2="530" y2="550" class="flow-arrow"/>
  <line x1="650" y1="550" x2="680" y2="550" class="flow-arrow"/>
  <line x1="800" y1="550" x2="830" y2="550" class="flow-arrow"/>
  <line x1="950" y1="550" x2="980" y2="550" class="flow-arrow"/>
  
  <!-- 循环箭头 -->
  <path d="M 890 520 Q 890 480 440 480 Q 380 480 380 520" class="flow-arrow"/>
  <text x="635" y="475" text-anchor="middle" class="small-text">循环执行直到完成或达到最大步数</text>
  
  <!-- 关键特性 -->
  <rect x="50" y="720" width="1100" height="60" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" rx="4"/>
  <text x="600" y="740" text-anchor="middle" class="subtitle" fill="white">关键特性</text>
  <text x="80" y="760" class="text" fill="white">• 支持Function Call和Struct Parse两种工具调用模式</text>
  <text x="400" y="760" class="text" fill="white">• 智能上下文截断避免Token超限</text>
  <text x="700" y="760" class="text" fill="white">• 多引擎并发网络搜索</text>
  <text x="950" y="760" class="text" fill="white">• SSE实时流式输出</text>
  
  <!-- 连接线 -->
  <line x1="250" y1="180" x2="300" y2="120" class="data-arrow"/>
  <line x1="550" y1="120" x2="600" y2="120" class="data-arrow"/>
  <line x1="800" y1="120" x2="850" y2="120" class="data-arrow"/>
  <line x1="425" y1="240" x2="200" y2="280" class="data-arrow"/>
  <line x1="525" y1="240" x2="575" y2="280" class="data-arrow"/>
  <line x1="625" y1="240" x2="975" y2="280" class="data-arrow"/>
</svg>
