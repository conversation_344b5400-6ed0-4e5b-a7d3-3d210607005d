<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .llm-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .logic-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .prompt-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 6; }
      .decision-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 6; }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .decision-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" class="title">任务完成判断机制 - LLM驱动的智能决策</text>
  
  <!-- 核心判断机制 -->
  <rect x="50" y="60" width="1300" height="80" class="llm-box"/>
  <text x="700" y="85" text-anchor="middle" class="subtitle" fill="white">🔥 核心机制：LLM通过next_step_prompt判断任务是否完成</text>
  <text x="70" y="105" class="text" fill="white">• LLM在think()阶段分析当前状态和历史对话</text>
  <text x="400" y="105" class="text" fill="white">• 根据prompt指令决定是否调用工具</text>
  <text x="700" y="105" class="text" fill="white">• 如果不调用工具(toolCalls为空)，则认为任务完成</text>
  <text x="1050" y="105" class="text" fill="white">• 设置AgentState.FINISHED</text>
  <text x="70" y="125" class="text" fill="white">• 这是一个完全由LLM驱动的智能判断过程，没有硬编码的完成条件</text>
  
  <!-- 判断流程 -->
  <rect x="50" y="180" width="1300" height="200" class="logic-box"/>
  <text x="700" y="200" text-anchor="middle" class="subtitle" fill="white">任务完成判断流程</text>
  
  <!-- 步骤1 -->
  <rect x="80" y="220" width="200" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="180" y="240" text-anchor="middle" class="text">1. think()调用LLM</text>
  <text x="90" y="255" class="small-text">• 传入完整Memory历史</text>
  <text x="90" y="270" class="small-text">• 使用next_step_prompt</text>
  <text x="90" y="285" class="small-text">• LLM分析当前状态</text>
  
  <!-- 步骤2 -->
  <rect x="320" y="220" width="200" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="420" y="240" text-anchor="middle" class="text">2. LLM智能判断</text>
  <text x="330" y="255" class="small-text">• 分析任务是否完成</text>
  <text x="330" y="270" class="small-text">• 决定是否需要工具</text>
  <text x="330" y="285" class="small-text">• 返回ToolCalls列表</text>
  
  <!-- 步骤3 -->
  <rect x="560" y="220" width="200" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="660" y="240" text-anchor="middle" class="text">3. 检查ToolCalls</text>
  <text x="570" y="255" class="small-text">• toolCalls.isEmpty()?</text>
  <text x="570" y="270" class="small-text">• 空 = 任务完成</text>
  <text x="570" y="285" class="small-text">• 非空 = 继续执行</text>
  
  <!-- 步骤4 -->
  <rect x="800" y="220" width="200" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="900" y="240" text-anchor="middle" class="text">4. 设置完成状态</text>
  <text x="810" y="255" class="small-text">• setState(FINISHED)</text>
  <text x="810" y="270" class="small-text">• 返回最终结果</text>
  <text x="810" y="285" class="small-text">• 结束执行循环</text>
  
  <!-- 步骤5 -->
  <rect x="1040" y="220" width="200" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="4"/>
  <text x="1140" y="240" text-anchor="middle" class="text">5. 输出完成信息</text>
  <text x="1050" y="255" class="small-text">• taskCompleteDesc</text>
  <text x="1050" y="270" class="small-text">• 或LLM最后回复</text>
  <text x="1050" y="285" class="small-text">• SSE流式输出</text>
  
  <!-- 流程箭头 -->
  <line x1="280" y1="260" x2="320" y2="260" class="flow-arrow"/>
  <line x1="520" y1="260" x2="560" y2="260" class="flow-arrow"/>
  <line x1="760" y1="260" x2="800" y2="260" class="flow-arrow"/>
  <line x1="1000" y1="260" x2="1040" y2="260" class="flow-arrow"/>
  
  <!-- 关键Prompt内容 -->
  <rect x="50" y="420" width="650" height="180" class="prompt-box"/>
  <text x="375" y="440" text-anchor="middle" class="subtitle" fill="white">关键Prompt内容 (next_step_prompt)</text>
  <text x="70" y="460" class="text" fill="white">React Agent:</text>
  <text x="70" y="475" class="small-text" fill="white">"根据当前状态和可用工具，确定下一步行动，根据之前的执行结果，继续完成用户的任务"</text>
  <text x="70" y="490" class="small-text" fill="white">"先判断任务是否已经完成："</text>
  <text x="70" y="505" class="small-text" fill="white">"- 如果当前任务已完成，则不调用工具。"</text>
  <text x="70" y="520" class="small-text" fill="white">"- 如果当前任务未完成，尽可能使用工具调用来完成任务。"</text>
  <text x="70" y="540" class="text" fill="white">Executor Agent:</text>
  <text x="70" y="555" class="small-text" fill="white">"判断任务是否已经完成："</text>
  <text x="70" y="570" class="small-text" fill="white">"- 当前任务已完成，则不调用工具。"</text>
  <text x="70" y="585" class="small-text" fill="white">"- 当前任务未完成，尽可能使用工具调用来尽可能完成当前任务"</text>
  
  <!-- 特殊情况处理 -->
  <rect x="720" y="420" width="630" height="180" class="decision-box"/>
  <text x="1035" y="440" text-anchor="middle" class="subtitle" fill="white">特殊情况和辅助判断</text>
  <text x="740" y="460" class="text" fill="white">1. 代码执行工具的完成判断:</text>
  <text x="750" y="475" class="small-text" fill="white">• FinalAnswerCheck类检查执行日志</text>
  <text x="750" y="490" class="small-text" fill="white">• 返回is_final_answer标志</text>
  <text x="740" y="510" class="text" fill="white">2. Planning Agent的完成判断:</text>
  <text x="750" y="525" class="small-text" fill="white">• 检查所有步骤状态是否为"completed"</text>
  <text x="750" y="540" class="small-text" fill="white">• allComplete = true时设置FINISHED</text>
  <text x="740" y="560" class="text" fill="white">3. 错误处理:</text>
  <text x="750" y="575" class="small-text" fill="white">• 异常时自动设置AgentState.FINISHED</text>
  <text x="750" y="590" class="small-text" fill="white">• 达到maxSteps时强制终止</text>
  
  <!-- 决策逻辑图 -->
  <rect x="50" y="640" width="1300" height="200" class="logic-box"/>
  <text x="700" y="660" text-anchor="middle" class="subtitle" fill="white">LLM决策逻辑流程图</text>
  
  <!-- 决策节点 -->
  <rect x="100" y="680" width="150" height="60" fill="#fff" stroke="#34495e" stroke-width="2" rx="4"/>
  <text x="175" y="700" text-anchor="middle" class="text">LLM接收</text>
  <text x="175" y="715" text-anchor="middle" class="small-text">Memory + Prompt</text>
  <text x="175" y="730" text-anchor="middle" class="small-text">+ 工具列表</text>
  
  <polygon points="320,680 420,710 320,740 220,710" fill="#fff" stroke="#34495e" stroke-width="2"/>
  <text x="320" y="705" text-anchor="middle" class="text">任务完成?</text>
  <text x="320" y="720" text-anchor="middle" class="small-text">LLM判断</text>
  
  <rect x="500" y="680" width="150" height="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="4"/>
  <text x="575" y="700" text-anchor="middle" class="text" fill="white">返回空</text>
  <text x="575" y="715" text-anchor="middle" class="small-text" fill="white">toolCalls=[]</text>
  <text x="575" y="730" text-anchor="middle" class="small-text" fill="white">任务完成</text>
  
  <rect x="500" y="760" width="150" height="60" fill="#2ecc71" stroke="#27ae60" stroke-width="2" rx="4"/>
  <text x="575" y="780" text-anchor="middle" class="text" fill="white">返回工具调用</text>
  <text x="575" y="795" text-anchor="middle" class="small-text" fill="white">toolCalls=[...]</text>
  <text x="575" y="810" text-anchor="middle" class="small-text" fill="white">继续执行</text>
  
  <rect x="750" y="680" width="150" height="60" fill="#95a5a6" stroke="#7f8c8d" stroke-width="2" rx="4"/>
  <text x="825" y="700" text-anchor="middle" class="text" fill="white">设置FINISHED</text>
  <text x="825" y="715" text-anchor="middle" class="small-text" fill="white">state=FINISHED</text>
  <text x="825" y="730" text-anchor="middle" class="small-text" fill="white">退出循环</text>
  
  <rect x="750" y="760" width="150" height="60" fill="#95a5a6" stroke="#7f8c8d" stroke-width="2" rx="4"/>
  <text x="825" y="780" text-anchor="middle" class="text" fill="white">执行工具</text>
  <text x="825" y="795" text-anchor="middle" class="small-text" fill="white">act()阶段</text>
  <text x="825" y="810" text-anchor="middle" class="small-text" fill="white">下一个step</text>
  
  <!-- 决策箭头 -->
  <line x1="250" y1="710" x2="270" y2="710" class="flow-arrow"/>
  <line x1="370" y1="690" x2="500" y2="710" class="decision-arrow"/>
  <line x1="370" y1="730" x2="500" y2="790" class="decision-arrow"/>
  <line x1="650" y1="710" x2="750" y2="710" class="flow-arrow"/>
  <line x1="650" y1="790" x2="750" y2="790" class="flow-arrow"/>
  
  <!-- 标签 -->
  <text x="435" y="685" text-anchor="middle" class="small-text">是</text>
  <text x="435" y="815" text-anchor="middle" class="small-text">否</text>
  
  <!-- 循环箭头 -->
  <path d="M 825 820 Q 825 850 175 850 Q 100 850 100 740" class="flow-arrow"/>
  <text x="450" y="865" text-anchor="middle" class="small-text">继续下一个step，重新判断</text>
  
  <!-- 总结 -->
  <rect x="50" y="870" width="1300" height="20" fill="#34495e" stroke="#2c3e50" stroke-width="1" rx="4"/>
  <text x="700" y="885" text-anchor="middle" class="text" fill="white">总结：任务完成完全由LLM智能判断，通过分析对话历史和当前状态，决定是否需要继续调用工具</text>
</svg>
