<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .header-text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: white; }
      .mcp-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .tool-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .architecture-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .comparison-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 8; }
      .protocol-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; rx: 8; }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .config-box { fill: #95a5a6; stroke: #7f8c8d; stroke-width: 2; rx: 8; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">MCP vs 普通Tool 对比分析</text>
  
  <!-- MCP架构 -->
  <rect x="50" y="60" width="750" height="280" class="mcp-box"/>
  <text x="425" y="85" text-anchor="middle" class="header-text">MCP (Model Context Protocol) 架构</text>
  
  <text x="70" y="110" class="text" fill="white">1. MCP协议标准</text>
  <text x="80" y="125" class="small-text" fill="white">• 基于SSE (Server-Sent Events) 协议</text>
  <text x="80" y="140" class="small-text" fill="white">• 支持实时双向通信</text>
  <text x="80" y="155" class="small-text" fill="white">• 标准化的工具调用接口</text>
  
  <text x="70" y="175" class="text" fill="white">2. 三层架构设计</text>
  <text x="80" y="190" class="small-text" fill="white">• Agent → MCP Client → MCP Server</text>
  <text x="80" y="205" class="small-text" fill="white">• 解耦Agent与具体工具实现</text>
  <text x="80" y="220" class="small-text" fill="white">• 支持远程工具服务</text>
  
  <text x="70" y="240" class="text" fill="white">3. 动态工具发现</text>
  <text x="80" y="255" class="small-text" fill="white">• listTool() - 动态获取可用工具列表</text>
  <text x="80" y="270" class="small-text" fill="white">• callTool() - 统一的工具调用接口</text>
  <text x="80" y="285" class="small-text" fill="white">• 支持多MCP服务器并行</text>
  
  <text x="70" y="305" class="text" fill="white">4. 配置示例</text>
  <text x="80" y="320" class="small-text" fill="white">mcp_client_url: "http://127.0.0.1:8188"</text>
  <text x="80" y="335" class="small-text" fill="white">mcp_server_url: "https://mcp.api-inference.modelscope.net/xxx/sse"</text>
  
  <!-- 普通Tool架构 -->
  <rect x="850" y="60" width="700" height="280" class="tool-box"/>
  <text x="1200" y="85" text-anchor="middle" class="header-text">普通Tool架构</text>
  
  <text x="870" y="110" class="text" fill="white">1. 直接集成模式</text>
  <text x="880" y="125" class="small-text" fill="white">• 实现BaseTool接口</text>
  <text x="880" y="140" class="small-text" fill="white">• 编译时静态绑定</text>
  <text x="880" y="155" class="small-text" fill="white">• 本地方法调用</text>
  
  <text x="870" y="175" class="text" fill="white">2. 内置工具类型</text>
  <text x="880" y="190" class="small-text" fill="white">• SearchTool - 搜索工具</text>
  <text x="880" y="205" class="small-text" fill="white">• CodeInterpreterTool - 代码执行</text>
  <text x="880" y="220" class="small-text" fill="white">• FileTool - 文件操作</text>
  <text x="880" y="235" class="small-text" fill="white">• ReportTool - 报告生成</text>
  <text x="880" y="250" class="small-text" fill="white">• DeepSearchTool - 深度搜索</text>
  
  <text x="870" y="270" class="text" fill="white">3. 工具管理</text>
  <text x="880" y="285" class="small-text" fill="white">• ToolCollection统一管理</text>
  <text x="880" y="300" class="small-text" fill="white">• 静态注册到toolMap</text>
  <text x="880" y="315" class="small-text" fill="white">• 直接execute()调用</text>
  <text x="880" y="330" class="small-text" fill="white">• 与数字员工系统集成</text>
  
  <!-- 核心区别对比 -->
  <rect x="50" y="370" width="1500" height="200" class="comparison-box"/>
  <text x="800" y="395" text-anchor="middle" class="header-text">核心区别对比</text>
  
  <!-- 表头 -->
  <rect x="70" y="410" width="150" height="30" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
  <text x="145" y="430" text-anchor="middle" class="header-text">对比维度</text>
  
  <rect x="220" y="410" width="400" height="30" fill="#e74c3c" stroke="#c0392b" stroke-width="1"/>
  <text x="420" y="430" text-anchor="middle" class="header-text">MCP工具</text>
  
  <rect x="620" y="410" width="400" height="30" fill="#3498db" stroke="#2980b9" stroke-width="1"/>
  <text x="820" y="430" text-anchor="middle" class="header-text">普通Tool</text>
  
  <rect x="1020" y="410" width="400" height="30" fill="#2ecc71" stroke="#27ae60" stroke-width="1"/>
  <text x="1220" y="430" text-anchor="middle" class="header-text">优势对比</text>
  
  <!-- 行1: 部署方式 -->
  <rect x="70" y="440" width="150" height="30" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="145" y="460" text-anchor="middle" class="text">部署方式</text>
  
  <rect x="220" y="440" width="400" height="30" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="420" y="460" text-anchor="middle" class="small-text">远程服务，独立部署，SSE协议通信</text>
  
  <rect x="620" y="440" width="400" height="30" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="820" y="460" text-anchor="middle" class="small-text">本地集成，编译时绑定，直接调用</text>
  
  <rect x="1020" y="440" width="400" height="30" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="1220" y="460" text-anchor="middle" class="small-text">MCP: 灵活扩展 | Tool: 性能更好</text>
  
  <!-- 行2: 工具发现 -->
  <rect x="70" y="470" width="150" height="30" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="145" y="490" text-anchor="middle" class="text">工具发现</text>
  
  <rect x="220" y="470" width="400" height="30" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="420" y="490" text-anchor="middle" class="small-text">动态发现，运行时获取工具列表</text>
  
  <rect x="620" y="470" width="400" height="30" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="820" y="490" text-anchor="middle" class="small-text">静态注册，编译时确定工具集合</text>
  
  <rect x="1020" y="470" width="400" height="30" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="1220" y="490" text-anchor="middle" class="small-text">MCP: 动态扩展 | Tool: 稳定可靠</text>
  
  <!-- 行3: 调用方式 -->
  <rect x="70" y="500" width="150" height="30" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="145" y="520" text-anchor="middle" class="text">调用方式</text>
  
  <rect x="220" y="500" width="400" height="30" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="420" y="520" text-anchor="middle" class="small-text">HTTP请求，JSON序列化，网络通信</text>
  
  <rect x="620" y="500" width="400" height="30" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="820" y="520" text-anchor="middle" class="small-text">直接方法调用，内存传递，本地执行</text>
  
  <rect x="1020" y="500" width="400" height="30" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="1220" y="520" text-anchor="middle" class="small-text">MCP: 跨语言支持 | Tool: 零延迟</text>
  
  <!-- 行4: 扩展性 -->
  <rect x="70" y="530" width="150" height="30" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1"/>
  <text x="145" y="550" text-anchor="middle" class="text">扩展性</text>
  
  <rect x="220" y="530" width="400" height="30" fill="#fadbd8" stroke="#e74c3c" stroke-width="1"/>
  <text x="420" y="550" text-anchor="middle" class="small-text">热插拔，无需重启，支持第三方服务</text>
  
  <rect x="620" y="530" width="400" height="30" fill="#d6eaf8" stroke="#3498db" stroke-width="1"/>
  <text x="820" y="550" text-anchor="middle" class="small-text">需要重新编译部署，代码级集成</text>
  
  <rect x="1020" y="530" width="400" height="30" fill="#d5f4e6" stroke="#2ecc71" stroke-width="1"/>
  <text x="1220" y="550" text-anchor="middle" class="small-text">MCP: 极高扩展性 | Tool: 深度集成</text>
  
  <!-- ToolCollection统一管理 -->
  <rect x="50" y="590" width="1500" height="180" class="architecture-box"/>
  <text x="800" y="615" text-anchor="middle" class="header-text">ToolCollection统一管理机制</text>
  
  <text x="70" y="640" class="text" fill="white">1. 双重工具管理</text>
  <text x="80" y="655" class="small-text" fill="white">• Map&lt;String, BaseTool&gt; toolMap - 管理普通工具</text>
  <text x="80" y="670" class="small-text" fill="white">• Map&lt;String, McpToolInfo&gt; mcpToolMap - 管理MCP工具</text>
  
  <text x="70" y="690" class="text" fill="white">2. 统一执行接口</text>
  <text x="80" y="705" class="small-text" fill="white">• execute(String name, Object toolInput) - 统一调用入口</text>
  <text x="80" y="720" class="small-text" fill="white">• 自动识别工具类型并路由到对应执行逻辑</text>
  
  <text x="70" y="740" class="text" fill="white">3. 数字员工集成</text>
  <text x="80" y="755" class="small-text" fill="white">• getDigitalEmployee(toolName) - 获取工具对应的数字员工</text>
  
  <!-- MCP协议流程 -->
  <rect x="50" y="790" width="750" height="200" class="protocol-box"/>
  <text x="425" y="815" text-anchor="middle" class="header-text">MCP协议调用流程</text>
  
  <text x="70" y="840" class="text" fill="white">步骤1: 工具发现</text>
  <text x="80" y="855" class="small-text" fill="white">McpTool.listTool(mcpServerUrl) → 获取可用工具列表</text>
  
  <text x="70" y="875" class="text" fill="white">步骤2: 工具注册</text>
  <text x="80" y="890" class="small-text" fill="white">ToolCollection.addMcpTool() → 注册到mcpToolMap</text>
  
  <text x="70" y="910" class="text" fill="white">步骤3: 工具调用</text>
  <text x="80" y="925" class="small-text" fill="white">McpTool.callTool(serverUrl, toolName, input) → HTTP调用</text>
  
  <text x="70" y="945" class="text" fill="white">步骤4: 结果返回</text>
  <text x="80" y="960" class="small-text" fill="white">JSON响应 → 解析结果 → 返回给Agent</text>
  
  <!-- 普通Tool流程 -->
  <rect x="850" y="790" width="700" height="200" class="tool-box"/>
  <text x="1200" y="815" text-anchor="middle" class="header-text">普通Tool调用流程</text>
  
  <text x="870" y="840" class="text" fill="white">步骤1: 工具实现</text>
  <text x="880" y="855" class="small-text" fill="white">实现BaseTool接口 → getName(), getDescription(), execute()</text>
  
  <text x="870" y="875" class="text" fill="white">步骤2: 工具注册</text>
  <text x="880" y="890" class="small-text" fill="white">ToolCollection.addTool() → 注册到toolMap</text>
  
  <text x="870" y="910" class="text" fill="white">步骤3: 工具调用</text>
  <text x="880" y="925" class="small-text" fill="white">BaseTool.execute(input) → 直接方法调用</text>
  
  <text x="870" y="945" class="text" fill="white">步骤4: 结果返回</text>
  <text x="880" y="960" class="small-text" fill="white">直接返回对象 → 无序列化开销</text>
  
  <!-- 使用场景建议 -->
  <rect x="50" y="1010" width="1500" height="150" class="config-box"/>
  <text x="800" y="1035" text-anchor="middle" class="header-text">使用场景建议</text>
  
  <text x="70" y="1060" class="text" fill="white">🔧 MCP工具适用场景:</text>
  <text x="80" y="1075" class="small-text" fill="white">• 第三方服务集成 (如12306票务、天气查询、股票信息等)</text>
  <text x="80" y="1090" class="small-text" fill="white">• 跨语言工具调用 (Python/Node.js/Go等不同语言实现的工具)</text>
  <text x="80" y="1105" class="small-text" fill="white">• 动态工具扩展 (运行时添加新工具，无需重启系统)</text>
  
  <text x="70" y="1125" class="text" fill="white">⚡ 普通Tool适用场景:</text>
  <text x="80" y="1140" class="small-text" fill="white">• 核心业务逻辑 (搜索、代码执行、文件操作、报告生成等)</text>
  <text x="80" y="1155" class="small-text" fill="white">• 高频调用场景 (性能敏感，需要最小延迟)</text>
  
  <!-- 连接箭头 -->
  <line x1="425" y1="340" x2="425" y2="370" class="flow-arrow"/>
  <line x1="1200" y1="340" x2="1200" y2="370" class="flow-arrow"/>
  <line x1="800" y1="570" x2="800" y2="590" class="flow-arrow"/>
  <line x1="425" y1="770" x2="425" y2="790" class="flow-arrow"/>
  <line x1="1200" y1="770" x2="1200" y2="790" class="flow-arrow"/>
  <line x1="800" y1="990" x2="800" y2="1010" class="flow-arrow"/>
</svg>
