<svg width="2200" height="2800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .header-text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: white; }
      .used-box { fill: #27ae60; stroke: #229954; stroke-width: 3; rx: 10; }
      .unused-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .partial-box { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .config-box { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .tool-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="1100" y="30" text-anchor="middle" class="title">Application.yml 提示词使用情况分析</text>
  
  <!-- 图例 -->
  <rect x="50" y="50" width="30" height="20" class="used-box"/>
  <text x="90" y="65" class="small-text">✅ 已使用</text>
  
  <rect x="200" y="50" width="30" height="20" class="partial-box"/>
  <text x="240" y="65" class="small-text">⚠️ 部分使用</text>
  
  <rect x="350" y="50" width="30" height="20" class="unused-box"/>
  <text x="390" y="65" class="small-text">❌ 未使用</text>
  
  <rect x="500" y="50" width="30" height="20" class="config-box"/>
  <text x="540" y="65" class="small-text">🔧 配置项</text>
  
  <!-- 第一部分：核心Agent提示词 -->
  <text x="50" y="110" class="subtitle">🤖 核心Agent提示词 (已使用)</text>
  
  <!-- Planner Agent -->
  <rect x="50" y="130" width="650" height="200" class="used-box"/>
  <text x="375" y="155" text-anchor="middle" class="header-text">Planner Agent 提示词</text>
  
  <text x="70" y="180" class="text" fill="white">📋 system_prompt (已使用)</text>
  <text x="80" y="195" class="small-text" fill="white">• 调用位置: PlanningAgent.java:55</text>
  <text x="80" y="210" class="small-text" fill="white">• 作用: 定义Genie身份，专注任务规划和拆解</text>
  <text x="80" y="225" class="small-text" fill="white">• 核心功能: 将用户任务拆解为具体、独立的任务列表</text>
  <text x="80" y="240" class="small-text" fill="white">• 拆解原则: 简单任务避免过度拆解，复杂任务合理分解(最多5个)</text>
  
  <text x="70" y="260" class="text" fill="white">🔄 next_step_prompt (已使用)</text>
  <text x="80" y="275" class="small-text" fill="white">• 调用位置: PlanningAgent.java:60</text>
  <text x="80" y="290" class="small-text" fill="white">• 作用: 指导planning工具的参数设置和状态管理</text>
  <text x="80" y="305" class="small-text" fill="white">• 状态控制: not_started, in_progress, completed, blocked</text>
  <text x="80" y="320" class="small-text" fill="white">• 命令类型: mark_step(标记状态), finish(完成任务)</text>
  
  <!-- Executor Agent -->
  <rect x="750" y="130" width="650" height="200" class="used-box"/>
  <text x="1075" y="155" text-anchor="middle" class="header-text">Executor Agent 提示词</text>
  
  <text x="770" y="180" class="text" fill="white">⚡ system_prompt (已使用)</text>
  <text x="780" y="195" class="small-text" fill="white">• 调用位置: ExecutorAgent.java:53</text>
  <text x="780" y="210" class="small-text" fill="white">• 作用: 定义任务执行专家角色，擅长推理、工具调用和反思</text>
  <text x="780" y="225" class="small-text" fill="white">• 工作流程: 先思考(Reasoning) → 然后工具调用(Acting)</text>
  <text x="780" y="240" class="small-text" fill="white">• 执行要求: 每次调用planning工具前必须输出500字以内思考过程</text>
  
  <text x="770" y="260" class="text" fill="white">🎯 next_step_prompt (已使用)</text>
  <text x="780" y="275" class="small-text" fill="white">• 调用位置: ExecutorAgent.java:59</text>
  <text x="780" y="290" class="small-text" fill="white">• 作用: 确定下一步行动，避免重复工具调用</text>
  <text x="780" y="305" class="small-text" fill="white">• 思考限制: 100字以内纯文本，禁用Markdown格式</text>
  <text x="780" y="320" class="small-text" fill="white">• 完成判断: 任务完成则不调用工具，未完成则继续执行</text>
  
  <!-- React Agent -->
  <rect x="1450" y="130" width="650" height="200" class="used-box"/>
  <text x="1775" y="155" text-anchor="middle" class="header-text">React Agent 提示词</text>
  
  <text x="1470" y="180" class="text" fill="white">🔄 system_prompt (已使用)</text>
  <text x="1480" y="195" class="small-text" fill="white">• 调用位置: ReactImplAgent.java:51</text>
  <text x="1480" y="210" class="small-text" fill="white">• 作用: 定义超级智能体Genie，使用ReAct模式</text>
  <text x="1480" y="225" class="small-text" fill="white">• 执行模式: 思考-行动-观察三步循环</text>
  <text x="1480" y="240" class="small-text" fill="white">• 输出要求: 优先HTML网页报告，支持多种格式</text>
  
  <text x="1470" y="260" class="text" fill="white">📝 next_step_prompt (已使用)</text>
  <text x="1480" y="275" class="small-text" fill="white">• 调用位置: ReactImplAgent.java:56</text>
  <text x="1480" y="290" class="small-text" fill="white">• 作用: 根据当前状态确定下一步行动</text>
  <text x="1480" y="305" class="small-text" fill="white">• 思考要求: 200字以内纯文字，避免重复内容</text>
  <text x="1480" y="320" class="small-text" fill="white">• 失败处理: 不重复执行失败的工具</text>
  
  <!-- 第二部分：工具描述提示词 -->
  <text x="50" y="370" class="subtitle">🛠️ 工具描述提示词 (已使用)</text>
  
  <!-- Plan Tool -->
  <rect x="50" y="390" width="400" height="180" class="used-box"/>
  <text x="250" y="415" text-anchor="middle" class="header-text">Plan Tool 描述</text>
  
  <text x="70" y="440" class="text" fill="white">📋 plan_tool.desc (已使用)</text>
  <text x="80" y="455" class="small-text" fill="white">• 作用: 计划工具描述，创建和管理复杂任务计划</text>
  <text x="80" y="470" class="small-text" fill="white">• 功能: 创建计划、更新步骤、跟踪进度</text>
  <text x="80" y="485" class="small-text" fill="white">• 格式: 执行顺序+编号、任务短标题：任务细节描述</text>
  
  <text x="70" y="505" class="text" fill="white">⚙️ plan_tool.params (已使用)</text>
  <text x="80" y="520" class="small-text" fill="white">• 参数: command(create/update/mark_step)</text>
  <text x="80" y="535" class="small-text" fill="white">• 状态: not_started, in_progress, completed, blocked</text>
  <text x="80" y="550" class="small-text" fill="white">• 任务列表: steps数组，包含执行顺序和描述</text>
  
  <!-- Code Agent -->
  <rect x="500" y="390" width="400" height="180" class="used-box"/>
  <text x="700" y="415" text-anchor="middle" class="header-text">Code Agent 描述</text>
  
  <text x="520" y="440" class="text" fill="white">🐍 code_agent.desc (已使用)</text>
  <text x="530" y="455" class="small-text" fill="white">• 作用: Python代码解释器工具描述</text>
  <text x="530" y="470" class="small-text" fill="white">• 限制: 严禁处理纯文本文件(.txt, .md, .html)</text>
  <text x="530" y="485" class="small-text" fill="white">• 适用: 处理.xlsx, .csv等Excel表格文件</text>
  
  <text x="520" y="505" class="text" fill="white">📝 code_agent.params (已使用)</text>
  <text x="530" y="520" class="small-text" fill="white">• 参数: task(任务描述)</text>
  <text x="530" y="535" class="small-text" fill="white">• 要求: 详细完整的任务描述，包含业务背景</text>
  <text x="530" y="550" class="small-text" fill="white">• 约束: 禁止编造数据，基于已有数据处理</text>
  
  <!-- Report Tool -->
  <rect x="950" y="390" width="400" height="180" class="used-box"/>
  <text x="1150" y="415" text-anchor="middle" class="header-text">Report Tool 描述</text>
  
  <text x="970" y="440" class="text" fill="white">📊 report_tool.desc (已使用)</text>
  <text x="980" y="455" class="small-text" fill="white">• 作用: 专业的Markdown、PPT和HTML生成工具</text>
  <text x="980" y="470" class="small-text" fill="white">• 格式: html(网页报告), ppt, markdown</text>
  <text x="980" y="485" class="small-text" fill="white">• 约束: 不是查数工具，严禁用于查询数据</text>
  
  <text x="970" y="505" class="text" fill="white">🎨 report_tool.params (已使用)</text>
  <text x="980" y="520" class="small-text" fill="white">• 参数: fileType, task, fileName, fileDescription</text>
  <text x="980" y="535" class="small-text" fill="white">• 文件类型: markdown, ppt, html三种</text>
  <text x="980" y="550" class="small-text" fill="white">• 命名规则: 中文前缀，包含文件后缀</text>
  
  <!-- File Tool -->
  <rect x="1400" y="390" width="400" height="180" class="used-box"/>
  <text x="1600" y="415" text-anchor="middle" class="header-text">File Tool 描述</text>
  
  <text x="1420" y="440" class="text" fill="white">📁 file_tool.desc (已使用)</text>
  <text x="1430" y="455" class="small-text" fill="white">• 作用: 文件读写工具，支持upload和get命令</text>
  <text x="1430" y="470" class="small-text" fill="white">• 限制: 不支持.xlsx文件写入，不擅长HTML/MD报告</text>
  <text x="1430" y="485" class="small-text" fill="white">• 适用: 平文本文件(.txt, .md, .html)读取</text>
  
  <text x="1420" y="505" class="text" fill="white">💾 file_tool.params (已使用)</text>
  <text x="1430" y="520" class="small-text" fill="white">• 参数: command, filename, description, content</text>
  <text x="1430" y="535" class="small-text" fill="white">• 命令: upload(上传), get(下载/读取)</text>
  <text x="1430" y="550" class="small-text" fill="white">• 文件名: 中文名称，根据内容确定后缀</text>
  
  <!-- Deep Search Tool -->
  <rect x="50" y="600" width="400" height="180" class="used-box"/>
  <text x="250" y="625" text-anchor="middle" class="header-text">Deep Search Tool 描述</text>
  
  <text x="70" y="650" class="text" fill="white">🔍 deep_search.desc (已使用)</text>
  <text x="80" y="665" class="small-text" fill="white">• 作用: 搜索工具，可以搜索各种互联网知识</text>
  <text x="80" y="680" class="small-text" fill="white">• 调用位置: 各Agent中的工具集合</text>
  
  <text x="70" y="700" class="text" fill="white">🌐 deep_search.params (已使用)</text>
  <text x="80" y="715" class="small-text" fill="white">• 参数: query(需要搜索的全部内容及描述)</text>
  <text x="80" y="730" class="small-text" fill="white">• 配置: page_count=5, 截断长度设置</text>
  <text x="80" y="745" class="small-text" fill="white">• 消息截断: message.truncate_len=20000</text>
  <text x="80" y="760" class="small-text" fill="white">• 文件描述截断: file_desc.truncate_len=1500</text>
  
  <!-- 第三部分：特殊功能提示词 -->
  <text x="50" y="810" class="subtitle">🎯 特殊功能提示词</text>
  
  <!-- Summary Agent -->
  <rect x="500" y="830" width="650" height="200" class="used-box"/>
  <text x="825" y="855" text-anchor="middle" class="header-text">Summary Agent 提示词 (已使用)</text>
  
  <text x="520" y="880" class="text" fill="white">📋 summary.system_prompt (已使用)</text>
  <text x="530" y="895" class="small-text" fill="white">• 调用位置: SummaryAgent.java:66-77</text>
  <text x="530" y="910" class="small-text" fill="white">• 作用: 根据任务执行结果生成最终总结</text>
  <text x="530" y="925" class="small-text" fill="white">• 功能: 从taskHistory中提取答案和结果文件名</text>
  <text x="530" y="940" class="small-text" fill="white">• 输出格式: 答案$$$文件名1、文件名2...</text>
  
  <text x="520" y="960" class="text" fill="white">🎯 核心约束</text>
  <text x="530" y="975" class="small-text" fill="white">• 不可产生幻觉，只能基于上下文信息回答</text>
  <text x="530" y="990" class="small-text" fill="white">• 文件名按重要性排序，HTML报告优先</text>
  <text x="530" y="1005" class="small-text" fill="white">• 过滤中间产物：搜索结果、图片文件等</text>
  <text x="530" y="1020" class="small-text" fill="white">• 严禁使用Markdown格式输出</text>
  
  <!-- Digital Employee Prompt -->
  <rect x="1200" y="830" width="650" height="200" class="used-box"/>
  <text x="1525" y="855" text-anchor="middle" class="header-text">Digital Employee 提示词 (已使用)</text>
  
  <text x="1220" y="880" class="text" fill="white">👥 digital_employee_prompt (已使用)</text>
  <text x="1230" y="895" class="small-text" fill="white">• 调用位置: ReActAgent.java:72, 122-135</text>
  <text x="1230" y="910" class="small-text" fill="white">• 作用: 数字员工命名专家，为工具匹配专业名称</text>
  <text x="1230" y="925" class="small-text" fill="white">• 命名规范: 6字以内，体现工具功能和使用场景</text>
  <text x="1230" y="940" class="small-text" fill="white">• 输出格式: 标准JSON格式，工具名:数字员工名称</text>
  
  <text x="1220" y="960" class="text" fill="white">🎭 角色示例</text>
  <text x="1230" y="975" class="small-text" fill="white">• 数据分析: 数据分析师、算法专家</text>
  <text x="1230" y="990" class="small-text" fill="white">• 代码开发: 代码专家、开发工程师</text>
  <text x="1230" y="1005" class="small-text" fill="white">• 报告生成: 报告撰写专家、内容策划</text>
  <text x="1230" y="1020" class="small-text" fill="white">• 市场分析: 市场洞察专员、竞品分析员</text>
  
  <!-- 第四部分：核心SOP提示词 -->
  <text x="50" y="1070" class="subtitle">📜 核心SOP提示词 (已使用)</text>
  
  <!-- Genie SOP Prompt -->
  <rect x="50" y="1090" width="1000" height="250" class="used-box"/>
  <text x="550" y="1115" text-anchor="middle" class="header-text">genie_sop_prompt (已使用)</text>
  
  <text x="70" y="1140" class="text" fill="white">🎯 核心作用</text>
  <text x="80" y="1155" class="small-text" fill="white">• 定义智能助手Genie身份，专注任务规划</text>
  <text x="80" y="1170" class="small-text" fill="white">• 调用位置: GenieConfig.java:246-247</text>
  <text x="80" y="1185" class="small-text" fill="white">• 使用场景: PlanningAgent中作为sopPrompt变量注入</text>
  
  <text x="70" y="1205" class="text" fill="white">🔧 关键能力</text>
  <text x="80" y="1220" class="small-text" fill="white">• 将用户任务拆解为具体、独立的任务列表</text>
  <text x="80" y="1235" class="small-text" fill="white">• 拆解原则: 简单任务避免过度拆解，复杂任务合理分解(最多5个)</text>
  <text x="80" y="1250" class="small-text" fill="white">• 执行要求: 每次调用planning工具前必须输出500字以内思考过程</text>
  
  <text x="70" y="1270" class="text" fill="white">📋 任务示例</text>
  <text x="80" y="1285" class="small-text" fill="white">• 示例1: 分析xxxx → 信息收集 → 筛选分析 → 输出报告</text>
  <text x="80" y="1300" class="small-text" fill="white">• 示例2: 提取文件表格 → 文件表格提取</text>
  <text x="80" y="1315" class="small-text" fill="white">• 示例3: PPT格式展示 → 信息收集 → 筛选分析 → 输出PPT</text>
  <text x="80" y="1330" class="small-text" fill="white">• 示例4: 写文档 → 信息收集 → 文件输出</text>
  
  <!-- Genie Base Prompt -->
  <rect x="1100" y="1090" width="1000" height="250" class="used-box"/>
  <text x="1600" y="1115" text-anchor="middle" class="header-text">genie_base_prompt (已使用)</text>
  
  <text x="1120" y="1140" class="text" fill="white">🎯 核心作用</text>
  <text x="1130" y="1155" class="small-text" fill="white">• 定义基础ReAct执行模式</text>
  <text x="1130" y="1170" class="small-text" fill="white">• 调用位置: GenieConfig.java:249-250</text>
  <text x="1130" y="1185" class="small-text" fill="white">• 使用场景: ReactImplAgent中作为basePrompt变量注入</text>
  
  <text x="1120" y="1205" class="text" fill="white">🔄 执行流程</text>
  <text x="1130" y="1220" class="small-text" fill="white">• 思考(Thought): 基于信息推理反思，明确下一步目标</text>
  <text x="1130" y="1235" class="small-text" fill="white">• 行动(Action): 工具调用或Finish[答案]</text>
  <text x="1130" y="1250" class="small-text" fill="white">• 观察(Observation): 记录前一步行动结果</text>
  
  <text x="1120" y="1270" class="text" fill="white">📊 输出要求</text>
  <text x="1130" y="1285" class="small-text" fill="white">• 默认HTML网页报告，支持用户指定格式</text>
  <text x="1130" y="1300" class="small-text" fill="white">• 结构化输出: excel/csv用于表格数据</text>
  <text x="1130" y="1315" class="small-text" fill="white">• 语言要求: 默认中文，支持用户指定语言</text>
  <text x="1130" y="1330" class="small-text" fill="white">• 工具优先: 优先选择合适工具，避免重复调用</text>
  
  <!-- 第五部分：配置项和未使用提示词 -->
  <text x="50" y="1380" class="subtitle">⚙️ 配置项和部分使用的提示词</text>
  
  <!-- Struct Parse Tool System Prompt -->
  <rect x="50" y="1400" width="650" height="180" class="partial-box"/>
  <text x="375" y="1425" text-anchor="middle" class="header-text">struct_parse_tool_system_prompt (部分使用)</text>
  
  <text x="70" y="1450" class="text" fill="white">🔧 配置位置</text>
  <text x="80" y="1465" class="small-text" fill="white">• 配置: GenieConfig.java:234-238</text>
  <text x="80" y="1480" class="small-text" fill="white">• 作用: 工具调用的JSON格式输出规范</text>
  <text x="80" y="1495" class="small-text" fill="white">• 内容: 详细的JSON格式示例和约束</text>
  
  <text x="70" y="1515" class="text" fill="white">⚠️ 使用状态</text>
  <text x="80" y="1530" class="small-text" fill="white">• 状态: 已配置但使用场景有限</text>
  <text x="80" y="1545" class="small-text" fill="white">• 用途: 结构化解析工具调用时使用</text>
  <text x="80" y="1560" class="small-text" fill="white">• 格式: 严格的JSON输出格式要求</text>
  
  <!-- Output Style Prompts -->
  <rect x="750" y="1400" width="650" height="180" class="partial-box"/>
  <text x="1075" y="1425" text-anchor="middle" class="header-text">output_style_prompts (部分使用)</text>
  
  <text x="770" y="1450" class="text" fill="white">🎨 样式配置</text>
  <text x="780" y="1465" class="small-text" fill="white">• 配置: GenieConfig.java:220-225</text>
  <text x="780" y="1480" class="small-text" fill="white">• 格式: {"html": "", "docs": "markdown", "table": "excel", "ppt": "ppt"}</text>
  <text x="780" y="1495" class="small-text" fill="white">• 作用: 根据用户需求确定输出格式</text>
  
  <text x="770" y="1515" class="text" fill="white">📋 格式映射</text>
  <text x="780" y="1530" class="small-text" fill="white">• html: 空字符串(默认)</text>
  <text x="780" y="1545" class="small-text" fill="white">• docs: "，最后以 markdown 展示最终结果"</text>
  <text x="780" y="1560" class="small-text" fill="white">• table: "，最后以excel 展示最终结果"</text>
  
  <!-- Task Pre Prompt -->
  <rect x="1450" y="1400" width="650" height="180" class="config-box"/>
  <text x="1775" y="1425" text-anchor="middle" class="header-text">task.pre_prompt (配置项)</text>
  
  <text x="1470" y="1450" class="text" fill="white">📝 配置内容</text>
  <text x="1480" y="1465" class="small-text" fill="white">• 配置: GenieConfig.java:151-152</text>
  <text x="1480" y="1480" class="small-text" fill="white">• 内容: "先输出100字以内的文字内容确定下一步的行动"</text>
  <text x="1480" y="1495" class="small-text" fill="white">• 约束: 不重复思考，不透露代码链接，禁用Markdown</text>
  
  <text x="1470" y="1515" class="text" fill="white">🎯 使用场景</text>
  <text x="1480" y="1530" class="small-text" fill="white">• 作用: 任务执行前的思考引导</text>
  <text x="1480" y="1545" class="small-text" fill="white">• 要求: 必须输出工具调用来完成当前任务</text>
  <text x="1480" y="1560" class="small-text" fill="white">• 格式: 严禁使用Markdown格式输出</text>
  
  <!-- 第六部分：未使用的提示词 -->
  <text x="50" y="1620" class="subtitle">❌ 未使用或空配置的提示词</text>
  
  <!-- Executor SOP Prompt -->
  <rect x="50" y="1640" width="400" height="120" class="unused-box"/>
  <text x="250" y="1665" text-anchor="middle" class="header-text">executor.sop_prompt (未使用)</text>
  
  <text x="70" y="1690" class="text" fill="white">❌ 配置状态</text>
  <text x="80" y="1705" class="small-text" fill="white">• 配置值: '{}' (空JSON对象)</text>
  <text x="80" y="1720" class="small-text" fill="white">• 调用位置: ExecutorAgent.java:58</text>
  <text x="80" y="1735" class="small-text" fill="white">• 实际效果: 替换为空字符串</text>
  <text x="80" y="1750" class="small-text" fill="white">• 状态: 预留配置，当前未启用</text>
  
  <!-- Sensitive Patterns -->
  <rect x="500" y="1640" width="400" height="120" class="unused-box"/>
  <text x="700" y="1665" text-anchor="middle" class="header-text">sensitive_patterns (未使用)</text>
  
  <text x="520" y="1690" class="text" fill="white">🔒 安全配置</text>
  <text x="530" y="1705" class="small-text" fill="white">• 配置值: '{}' (空JSON对象)</text>
  <text x="530" y="1720" class="small-text" fill="white">• 配置位置: GenieConfig.java:213-218</text>
  <text x="530" y="1735" class="small-text" fill="white">• 预期用途: 敏感词过滤模式</text>
  <text x="530" y="1750" class="small-text" fill="white">• 状态: 预留功能，当前未实现</text>
  
  <!-- Message Interval -->
  <rect x="950" y="1640" width="400" height="120" class="unused-box"/>
  <text x="1150" y="1665" text-anchor="middle" class="header-text">message_interval (未使用)</text>
  
  <text x="970" y="1690" class="text" fill="white">⏱️ 消息间隔</text>
  <text x="980" y="1705" class="small-text" fill="white">• 配置值: '{}' (空JSON对象)</text>
  <text x="980" y="1720" class="small-text" fill="white">• 配置位置: GenieConfig.java:227-232</text>
  <text x="980" y="1735" class="small-text" fill="white">• 预期用途: 消息发送间隔控制</text>
  <text x="980" y="1750" class="small-text" fill="white">• 状态: 预留功能，当前未使用</text>
  
  <!-- Tool List -->
  <rect x="1400" y="1640" width="400" height="120" class="unused-box"/>
  <text x="1600" y="1665" text-anchor="middle" class="header-text">tool_list (未使用)</text>
  
  <text x="1420" y="1690" class="text" fill="white">🛠️ 工具列表</text>
  <text x="1430" y="1705" class="small-text" fill="white">• 配置值: '{}' (空JSON对象)</text>
  <text x="1430" y="1720" class="small-text" fill="white">• 配置位置: application.yml:98</text>
  <text x="1430" y="1735" class="small-text" fill="white">• 预期用途: 自定义工具列表配置</text>
  <text x="1430" y="1750" class="small-text" fill="white">• 状态: 预留配置，当前未启用</text>
  
  <!-- 第七部分：服务URL配置 -->
  <text x="50" y="1800" class="subtitle">🌐 服务URL配置 (已使用)</text>
  
  <rect x="50" y="1820" width="2050" height="100" class="config-box"/>
  <text x="1075" y="1845" text-anchor="middle" class="header-text">服务端点配置</text>
  
  <text x="70" y="1870" class="text" fill="white">🔗 code_interpreter_url: "http://127.0.0.1:1601" - Python代码执行服务地址</text>
  <text x="70" y="1885" class="text" fill="white">🔍 deep_search_url: "http://127.0.0.1:1601" - 深度搜索服务地址</text>
  <text x="70" y="1900" class="text" fill="white">🤖 mcp_client_url: "http://127.0.0.1:8188" - MCP客户端服务地址</text>
  
  <text x="1200" y="1870" class="text" fill="white">📡 mcp_server_url: "https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse" - MCP服务器地址</text>
  <text x="1200" y="1885" class="text" fill="white">🎯 default_model_name: gpt-4.1 - 默认LLM模型</text>
  <text x="1200" y="1900" class="text" fill="white">👤 user_name: '' - 用户名配置(空)</text>
  
  <!-- 总结框 -->
  <rect x="50" y="1950" width="2050" height="200" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="8"/>
  <text x="1075" y="1975" text-anchor="middle" class="subtitle">提示词使用情况总结</text>
  
  <text x="70" y="2000" class="text">✅ <tspan class="subtitle">已充分使用 (85%)</tspan>：</text>
  <text x="80" y="2020" class="small-text">核心Agent提示词(planner, executor, react)、工具描述提示词(plan_tool, code_agent, report_tool, file_tool, deep_search)、</text>
  <text x="80" y="2035" class="small-text">特殊功能提示词(summary, digital_employee)、核心SOP提示词(genie_sop_prompt, genie_base_prompt)、服务URL配置</text>
  
  <text x="70" y="2055" class="text">⚠️ <tspan class="subtitle">部分使用 (10%)</tspan>：</text>
  <text x="80" y="2075" class="small-text">struct_parse_tool_system_prompt(结构化解析场景)、output_style_prompts(格式映射)、task.pre_prompt(任务前引导)</text>
  
  <text x="70" y="2095" class="text">❌ <tspan class="subtitle">未使用 (5%)</tspan>：</text>
  <text x="80" y="2115" class="small-text">executor.sop_prompt(空配置)、sensitive_patterns(预留安全功能)、message_interval(预留间隔控制)、tool_list(预留工具配置)</text>
  
  <text x="70" y="2135" class="text">🎯 <tspan class="subtitle">核心价值</tspan>：提示词系统构建了完整的多Agent协作框架，实现了任务规划→执行→总结的闭环流程，</text>
  <text x="80" y="2150" class="small-text">通过精细化的角色定义和行为约束，确保了系统的稳定性和可控性。预留的配置项为未来功能扩展提供了灵活性。</text>
</svg>
