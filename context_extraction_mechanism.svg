<svg width="1600" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .header-text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: white; }
      .memory-box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .token-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .truncate-box { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 8; }
      .strategy-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .tool-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; rx: 8; }
      .flow-arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .config-box { fill: #95a5a6; stroke: #7f8c8d; stroke-width: 2; rx: 8; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" text-anchor="middle" class="title">JoyAgent-JDGenie 长上下文提取机制</text>
  
  <!-- Memory类上下文管理 -->
  <rect x="50" y="60" width="500" height="200" class="memory-box"/>
  <text x="300" y="85" text-anchor="middle" class="header-text">Memory类 - 上下文管理核心</text>
  
  <text x="70" y="110" class="text" fill="white">1. 消息历史管理</text>
  <text x="80" y="125" class="small-text" fill="white">• List&lt;Message&gt; messages - 存储所有消息</text>
  <text x="80" y="140" class="small-text" fill="white">• addMessage() - 添加新消息</text>
  <text x="80" y="155" class="small-text" fill="white">• getLastMessage() - 获取最新消息</text>
  
  <text x="70" y="175" class="text" fill="white">2. 上下文清理机制</text>
  <text x="80" y="190" class="small-text" fill="white">• clearToolContext() - 清理工具执行历史</text>
  <text x="80" y="205" class="small-text" fill="white">• 移除TOOL角色消息和toolCalls</text>
  <text x="80" y="220" class="small-text" fill="white">• 删除特定模式的助手消息</text>
  <text x="80" y="235" class="small-text" fill="white">• 保留核心对话上下文</text>
  
  <!-- TokenCounter计算机制 -->
  <rect x="600" y="60" width="500" height="200" class="token-box"/>
  <text x="850" y="85" text-anchor="middle" class="header-text">TokenCounter - Token计算机制</text>
  
  <text x="620" y="110" class="text" fill="white">1. 精确Token计算</text>
  <text x="630" y="125" class="small-text" fill="white">• countText() - 文本Token计算</text>
  <text x="630" y="140" class="small-text" fill="white">• countImage() - 图像Token计算</text>
  <text x="630" y="155" class="small-text" fill="white">• countToolCalls() - 工具调用Token计算</text>
  
  <text x="620" y="175" class="text" fill="white">2. 消息Token统计</text>
  <text x="630" y="190" class="small-text" fill="white">• BASE_MESSAGE_TOKENS = 4 (基础)</text>
  <text x="630" y="205" class="small-text" fill="white">• countMessageTokens() - 单条消息</text>
  <text x="630" y="220" class="small-text" fill="white">• countListMessageTokens() - 消息列表</text>
  <text x="630" y="235" class="small-text" fill="white">• 支持文本、图像、工具调用混合计算</text>
  
  <!-- LLM截断机制 -->
  <rect x="1150" y="60" width="400" height="200" class="truncate-box"/>
  <text x="1350" y="85" text-anchor="middle" class="header-text">LLM截断机制</text>
  
  <text x="1170" y="110" class="text" fill="white">1. 智能截断策略</text>
  <text x="1180" y="125" class="small-text" fill="white">• truncateMessage() 方法</text>
  <text x="1180" y="140" class="small-text" fill="white">• 从最新消息开始倒序保留</text>
  <text x="1180" y="155" class="small-text" fill="white">• 保证system消息完整性</text>
  
  <text x="1170" y="175" class="text" fill="white">2. 对话完整性保证</text>
  <text x="1180" y="190" class="small-text" fill="white">• 确保以user消息开始</text>
  <text x="1180" y="205" class="small-text" fill="white">• 移除不完整的assistant消息</text>
  <text x="1180" y="220" class="small-text" fill="white">• maxInputTokens限制</text>
  <text x="1180" y="235" class="small-text" fill="white">• 保持对话逻辑连贯性</text>
  
  <!-- 配置参数 -->
  <rect x="50" y="290" width="1500" height="120" class="config-box"/>
  <text x="800" y="315" text-anchor="middle" class="header-text">关键配置参数</text>
  
  <text x="70" y="340" class="text" fill="white">• llm.default.max_input_tokens: 100000 (LLM最大输入Token)</text>
  <text x="70" y="355" class="text" fill="white">• autobots.autoagent.tool.file_tool.truncate_len: 5000 (文件工具内容截断长度)</text>
  <text x="70" y="370" class="text" fill="white">• autobots.autoagent.tool.deep_search.file_desc.truncate_len: 500 (深度搜索文件描述截断)</text>
  <text x="70" y="385" class="text" fill="white">• autobots.autoagent.tool.deep_search.message.truncate_len: 500 (深度搜索消息截断)</text>
  <text x="70" y="400" class="text" fill="white">• autobots.autoagent.summary.message_size_limit: 1000 (总结消息大小限制)</text>
  
  <!-- 工具级别的截断策略 -->
  <rect x="50" y="440" width="750" height="280" class="tool-box"/>
  <text x="425" y="465" text-anchor="middle" class="header-text">工具级别的上下文截断策略</text>
  
  <text x="70" y="490" class="text" fill="white">1. 文件工具 (FileTool)</text>
  <text x="80" y="505" class="small-text" fill="white">• 文件内容按fileToolContentTruncateLen截断</text>
  <text x="80" y="520" class="small-text" fill="white">• 保留文件关键信息和结构</text>
  
  <text x="70" y="540" class="text" fill="white">2. 深度搜索工具 (DeepSearchTool)</text>
  <text x="80" y="555" class="small-text" fill="white">• 搜索结果按deepSearchToolMessageTruncateLen截断</text>
  <text x="80" y="570" class="small-text" fill="white">• 文件描述按deepSearchToolFileDescTruncateLen截断</text>
  <text x="80" y="585" class="small-text" fill="white">• 保留搜索答案的核心内容</text>
  
  <text x="70" y="605" class="text" fill="white">3. 报告工具 (ReportTool)</text>
  <text x="80" y="620" class="small-text" fill="white">• truncate_files() 函数处理文件列表</text>
  <text x="80" y="635" class="small-text" fill="white">• 按模型上下文长度的80%截断</text>
  <text x="80" y="650" class="small-text" fill="white">• 优先保留关键文件内容</text>
  
  <text x="70" y="670" class="text" fill="white">4. Python工具截断机制</text>
  <text x="80" y="685" class="small-text" fill="white">• LLMModelInfoFactory.get_context_length()</text>
  <text x="80" y="700" class="small-text" fill="white">• 动态计算模型上下文长度</text>
  
  <!-- 智能提取策略 -->
  <rect x="850" y="440" width="700" height="280" class="strategy-box"/>
  <text x="1200" y="465" text-anchor="middle" class="header-text">智能上下文提取策略</text>
  
  <text x="870" y="490" class="text" fill="white">1. 分层截断策略</text>
  <text x="880" y="505" class="small-text" fill="white">• 系统消息 (System) - 最高优先级，始终保留</text>
  <text x="880" y="520" class="small-text" fill="white">• 最新对话 (Recent) - 高优先级，倒序保留</text>
  <text x="880" y="535" class="small-text" fill="white">• 工具结果 (Tool) - 可清理，按需保留</text>
  
  <text x="870" y="555" class="text" fill="white">2. 内容重要性评估</text>
  <text x="880" y="570" class="small-text" fill="white">• 用户查询 - 核心内容，必须保留</text>
  <text x="880" y="585" class="small-text" fill="white">• 助手回复 - 重要内容，优先保留</text>
  <text x="880" y="600" class="small-text" fill="white">• 工具调用 - 中间过程，可适当清理</text>
  
  <text x="870" y="620" class="text" fill="white">3. 动态调整机制</text>
  <text x="880" y="635" class="small-text" fill="white">• 根据模型上下文长度动态调整</text>
  <text x="880" y="650" class="small-text" fill="white">• clearToolMessage配置控制清理行为</text>
  <text x="880" y="665" class="small-text" fill="white">• 保持对话逻辑的连贯性</text>
  
  <text x="870" y="685" class="text" fill="white">4. 特殊模式处理</text>
  <text x="880" y="700" class="small-text" fill="white">• Claude模型特殊格式适配</text>
  <text x="880" y="715" class="small-text" fill="white">• 敏感词过滤和脱敏处理</text>
  
  <!-- 执行流程 -->
  <rect x="50" y="750" width="1500" height="200" class="memory-box"/>
  <text x="800" y="775" text-anchor="middle" class="header-text">上下文提取执行流程</text>
  
  <text x="70" y="800" class="text" fill="white">步骤1: 消息收集</text>
  <text x="80" y="815" class="small-text" fill="white">Memory.addMessage() → 收集所有对话消息、工具调用、系统消息</text>
  
  <text x="70" y="835" class="text" fill="white">步骤2: Token计算</text>
  <text x="80" y="850" class="small-text" fill="white">TokenCounter.countListMessageTokens() → 计算当前消息列表总Token数</text>
  
  <text x="70" y="870" class="text" fill="white">步骤3: 智能截断</text>
  <text x="80" y="885" class="small-text" fill="white">LLM.truncateMessage() → 根据maxInputTokens限制，倒序保留重要消息</text>
  
  <text x="70" y="905" class="text" fill="white">步骤4: 上下文清理</text>
  <text x="80" y="920" class="small-text" fill="white">Memory.clearToolContext() → 清理工具执行历史，保留核心对话</text>
  
  <text x="70" y="940" class="text" fill="white">步骤5: 格式化输出</text>
  <text x="80" y="955" class="small-text" fill="white">LLM.formatMessages() → 转换为LLM API格式，支持不同模型适配</text>
  
  <!-- 核心优势 -->
  <rect x="50" y="980" width="1500" height="180" class="strategy-box"/>
  <text x="800" y="1005" text-anchor="middle" class="header-text">长上下文提取的核心优势</text>
  
  <text x="70" y="1030" class="text" fill="white">🎯 精确性: TokenCounter提供准确的Token计算，避免超出模型限制</text>
  <text x="70" y="1050" class="text" fill="white">🧠 智能性: 分层截断策略，优先保留重要信息，维持对话连贯性</text>
  <text x="70" y="1070" class="text" fill="white">⚡ 高效性: 倒序截断算法，快速定位需要保留的消息范围</text>
  <text x="70" y="1090" class="text" fill="white">🔧 灵活性: 配置化参数控制，支持不同场景的截断需求</text>
  <text x="70" y="1110" class="text" fill="white">🛡️ 安全性: 敏感词过滤和脱敏处理，保护隐私信息</text>
  <text x="70" y="1130" class="text" fill="white">🔄 适配性: 支持多种LLM模型格式，包括OpenAI和Claude</text>
  <text x="70" y="1150" class="text" fill="white">📊 可控性: 工具级别的独立截断配置，精细化控制不同工具的上下文使用</text>
  
  <!-- 连接箭头 -->
  <line x1="300" y1="260" x2="300" y2="290" class="flow-arrow"/>
  <line x1="850" y1="260" x2="850" y2="290" class="flow-arrow"/>
  <line x1="1350" y1="260" x2="1350" y2="290" class="flow-arrow"/>
  <line x1="425" y1="410" x2="425" y2="440" class="flow-arrow"/>
  <line x1="1200" y1="410" x2="1200" y2="440" class="flow-arrow"/>
  <line x1="800" y1="720" x2="800" y2="750" class="flow-arrow"/>
  <line x1="800" y1="950" x2="800" y2="980" class="flow-arrow"/>
</svg>
