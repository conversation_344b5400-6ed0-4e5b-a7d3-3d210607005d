<svg width="1800" height="2200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .header-text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: white; }
      .planning-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .executor-box { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .tool-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 3; rx: 10; }
      .state-box { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .parallel-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .flow-arrow { stroke: #34495e; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .parallel-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
      .state-arrow { stroke: #f39c12; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 3,3; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">genie_sop_prompt 任务拆分后的执行流程</text>
  
  <!-- 第一阶段：Planning Agent 任务拆分 -->
  <text x="50" y="70" class="subtitle">阶段1: Planning Agent 任务拆分</text>
  
  <rect x="50" y="90" width="700" height="200" class="planning-box"/>
  <text x="400" y="115" text-anchor="middle" class="header-text">PlanningAgent - genie_sop_prompt 执行</text>
  
  <text x="70" y="140" class="text" fill="white">🧠 思考阶段 (think())</text>
  <text x="80" y="155" class="small-text" fill="white">• 输入500字以内思考过程 (reasoning)</text>
  <text x="80" y="170" class="small-text" fill="white">• 分析用户需求，识别核心需求及潜在挑战</text>
  <text x="80" y="185" class="small-text" fill="white">• 设计任务拆解策略：简单任务避免过度拆解，复杂任务合理分解</text>
  
  <text x="70" y="205" class="text" fill="white">⚡ 行动阶段 (act())</text>
  <text x="80" y="220" class="small-text" fill="white">• 调用planning工具，生成任务列表 (最多5个子任务)</text>
  <text x="80" y="235" class="small-text" fill="white">• 任务格式：["执行顺序1. 任务短标题: 任务描述", "执行顺序2. ..."]</text>
  <text x="80" y="250" class="small-text" fill="white">• 创建Plan对象，初始化任务状态为 not_started</text>
  
  <text x="70" y="270" class="text" fill="white">📋 输出结果</text>
  <text x="80" y="285" class="small-text" fill="white">• 返回任务列表字符串，用 &lt;sep&gt; 分隔多个任务</text>
  
  <!-- Planning Tool 详细说明 -->
  <rect x="800" y="90" width="500" height="200" class="tool-box"/>
  <text x="1050" y="115" text-anchor="middle" class="header-text">PlanningTool 工具执行</text>
  
  <text x="820" y="140" class="text" fill="white">🔧 命令类型</text>
  <text x="830" y="155" class="small-text" fill="white">• create: 创建新计划 (title, steps)</text>
  <text x="830" y="170" class="small-text" fill="white">• mark_step: 标记步骤状态 (step_status)</text>
  <text x="830" y="185" class="small-text" fill="white">• finish: 完成计划</text>
  
  <text x="820" y="205" class="text" fill="white">📊 状态管理</text>
  <text x="830" y="220" class="small-text" fill="white">• not_started: 未开始</text>
  <text x="830" y="235" class="small-text" fill="white">• in_progress: 进行中</text>
  <text x="830" y="250" class="small-text" fill="white">• completed: 已完成</text>
  
  <text x="820" y="270" class="text" fill="white">🔄 Plan对象</text>
  <text x="830" y="285" class="small-text" fill="white">• 维护任务列表、状态列表、当前步骤</text>
  
  <!-- 第二阶段：任务分发机制 -->
  <text x="50" y="330" class="subtitle">阶段2: 任务分发与执行策略</text>
  
  <rect x="50" y="350" width="1250" height="250" class="state-box"/>
  <text x="675" y="375" text-anchor="middle" class="header-text">PlanSolveHandlerImpl - 任务分发逻辑</text>
  
  <text x="70" y="400" class="text" fill="white">🔀 分发策略判断</text>
  <text x="80" y="415" class="small-text" fill="white">• 解析planning结果：planningResult.split("&lt;sep&gt;")</text>
  <text x="80" y="430" class="small-text" fill="white">• 任务前缀添加：task → "你的任务是：" + task</text>
  <text x="80" y="445" class="small-text" fill="white">• 执行策略选择：单任务 vs 多任务并行</text>
  
  <text x="70" y="465" class="text" fill="white">📝 单任务执行 (planningResults.size() == 1)</text>
  <text x="80" y="480" class="small-text" fill="white">• 直接调用：executorResult = executor.run(planningResults.get(0))</text>
  <text x="80" y="495" class="small-text" fill="white">• 顺序执行，共享内存和上下文</text>
  
  <text x="70" y="515" class="text" fill="white">🔄 多任务并行执行 (planningResults.size() > 1)</text>
  <text x="80" y="530" class="small-text" fill="white">• 创建多个ExecutorAgent副本 (slaveExecutors)</text>
  <text x="80" y="545" class="small-text" fill="white">• 并发执行：ThreadUtil.execute() + CountDownLatch</text>
  <text x="80" y="560" class="small-text" fill="white">• 内存合并：将所有slave的memory合并到主executor</text>
  <text x="80" y="575" class="small-text" fill="white">• 结果聚合：String.join("\\n", tmpTaskResult.values())</text>
  
  <!-- 第三阶段：ExecutorAgent 执行 -->
  <text x="50" y="640" class="subtitle">阶段3: ExecutorAgent 子任务执行</text>
  
  <rect x="50" y="660" width="600" height="280" class="executor-box"/>
  <text x="350" y="685" text-anchor="middle" class="header-text">ExecutorAgent 执行流程</text>
  
  <text x="70" y="710" class="text" fill="white">🎯 任务接收</text>
  <text x="80" y="725" class="small-text" fill="white">• 接收格式："你的任务是：[具体子任务描述]"</text>
  <text x="80" y="740" class="small-text" fill="white">• 添加前缀：taskPrePrompt + request</text>
  <text x="80" y="755" class="small-text" fill="white">• 更新上下文：context.setTask(request)</text>
  
  <text x="70" y="775" class="text" fill="white">🧠 思考阶段 (think())</text>
  <text x="80" y="790" class="small-text" fill="white">• 注入文件信息：formatFileInfo(productFiles)</text>
  <text x="80" y="805" class="small-text" fill="white">• 调用LLM：askTool() 获取工具调用决策</text>
  <text x="80" y="820" class="small-text" fill="white">• 工具选择：ToolChoice.AUTO 自动选择</text>
  
  <text x="70" y="840" class="text" fill="white">⚡ 行动阶段 (act())</text>
  <text x="80" y="855" class="small-text" fill="white">• 执行工具调用：executeTool(toolCall)</text>
  <text x="80" y="870" class="small-text" fill="white">• 结果记录：添加到memory中</text>
  <text x="80" y="885" class="small-text" fill="white">• 循环执行：直到任务完成或达到maxSteps</text>
  
  <text x="70" y="905" class="text" fill="white">✅ 完成判断</text>
  <text x="80" y="920" class="small-text" fill="white">• toolCalls为空：表示任务完成</text>
  <text x="80" y="935" class="small-text" fill="white">• 状态更新：AgentState.FINISHED</text>
  
  <!-- 数字员工系统 -->
  <rect x="700" y="660" width="600" height="280" class="executor-box"/>
  <text x="1000" y="685" text-anchor="middle" class="header-text">数字员工系统 (Digital Employee)</text>
  
  <text x="720" y="710" class="text" fill="white">👥 员工分配</text>
  <text x="730" y="725" class="small-text" fill="white">• 调用：generateDigitalEmployee(request)</text>
  <text x="730" y="740" class="small-text" fill="white">• 根据任务场景匹配专业角色</text>
  <text x="730" y="755" class="small-text" fill="white">• 工具人格化：file_tool → "数据记录员"</text>
  
  <text x="720" y="775" class="text" fill="white">🔧 工具映射</text>
  <text x="730" y="790" class="small-text" fill="white">• 数据分析场景：数据分析师、算法专家</text>
  <text x="730" y="805" class="small-text" fill="white">• 市场调研场景：市场洞察专员、竞品分析员</text>
  <text x="730" y="820" class="small-text" fill="white">• 代码开发场景：代码专家、开发工程师</text>
  
  <text x="720" y="840" class="text" fill="white">📋 动态命名</text>
  <text x="730" y="855" class="small-text" fill="white">• 基于用户原始任务和当前子任务</text>
  <text x="730" y="870" class="small-text" fill="white">• 6字以内专业名称</text>
  <text x="730" y="885" class="small-text" fill="white">• JSON格式输出：{"tool_name": "员工名称"}</text>
  
  <text x="720" y="905" class="text" fill="white">🎯 用户体验</text>
  <text x="730" y="920" class="small-text" fill="white">• 提升工具调用可理解性</text>
  <text x="730" y="935" class="small-text" fill="white">• 专业角色感知</text>
  
  <!-- 第四阶段：状态管理与进度跟踪 -->
  <text x="50" y="980" class="subtitle">阶段4: 状态管理与进度跟踪</text>
  
  <rect x="50" y="1000" width="800" height="300" class="state-box"/>
  <text x="450" y="1025" text-anchor="middle" class="header-text">Plan状态管理机制</text>
  
  <text x="70" y="1050" class="text" fill="white">📊 状态跟踪</text>
  <text x="80" y="1065" class="small-text" fill="white">• Plan.stepPlan(): 当前任务completed → 下一任务in_progress</text>
  <text x="80" y="1080" class="small-text" fill="white">• getCurrentStep(): 获取当前进行中的任务</text>
  <text x="80" y="1095" class="small-text" fill="white">• updateStepStatus(): 更新指定步骤状态</text>
  
  <text x="70" y="1115" class="text" fill="white">🔄 执行循环</text>
  <text x="80" y="1130" class="small-text" fill="white">• while (stepIdx <= maxStepNum): 最大步数控制</text>
  <text x="80" y="1145" class="small-text" fill="white">• 每轮执行后检查所有任务状态</text>
  <text x="80" y="1160" class="small-text" fill="white">• allComplete判断：所有任务status == "completed"</text>
  
  <text x="70" y="1180" class="text" fill="white">✅ 完成条件</text>
  <text x="80" y="1195" class="small-text" fill="white">• 所有子任务completed: setState(AgentState.FINISHED)</text>
  <text x="80" y="1210" class="small-text" fill="white">• 返回"finish": 表示整个计划完成</text>
  <text x="80" y="1225" class="small-text" fill="white">• 发送plan事件: printer.send("plan", planningTool.getPlan())</text>
  
  <text x="70" y="1245" class="text" fill="white">📤 结果输出</text>
  <text x="80" y="1260" class="small-text" fill="white">• 发送task事件: printer.send("task", step)</text>
  <text x="80" y="1275" class="small-text" fill="white">• SSE流式推送给前端</text>
  <text x="80" y="1290" class="small-text" fill="white">• 返回当前步骤: planningTool.getPlan().getCurrentStep()</text>
  
  <!-- 并行执行详细机制 -->
  <rect x="900" y="1000" width="800" height="300" class="parallel-box"/>
  <text x="1300" y="1025" text-anchor="middle" class="header-text">并行执行机制详解</text>
  
  <text x="920" y="1050" class="text" fill="white">🔀 并发策略</text>
  <text x="930" y="1065" class="small-text" fill="white">• 多任务时创建ExecutorAgent副本</text>
  <text x="930" y="1080" class="small-text" fill="white">• 每个副本独立执行一个子任务</text>
  <text x="930" y="1095" class="small-text" fill="white">• 共享初始状态和内存</text>
  
  <text x="920" y="1115" class="text" fill="white">🧵 线程管理</text>
  <text x="930" y="1130" class="small-text" fill="white">• ThreadUtil.execute(): 异步执行</text>
  <text x="930" y="1145" class="small-text" fill="white">• CountDownLatch: 等待所有任务完成</text>
  <text x="930" y="1160" class="small-text" fill="white">• ConcurrentHashMap: 线程安全结果收集</text>
  
  <text x="920" y="1180" class="text" fill="white">🔄 内存合并</text>
  <text x="930" y="1195" class="small-text" fill="white">• 记录初始内存索引: memoryIndex</text>
  <text x="930" y="1210" class="small-text" fill="white">• 合并新增消息: slaveExecutor.getMemory()</text>
  <text x="930" y="1225" class="small-text" fill="white">• 清理副本内存: slaveExecutor.getMemory().clear()</text>
  
  <text x="920" y="1245" class="text" fill="white">📊 结果聚合</text>
  <text x="930" y="1260" class="small-text" fill="white">• 收集所有任务结果: tmpTaskResult</text>
  <text x="930" y="1275" class="small-text" fill="white">• 合并输出: String.join("\\n", values)</text>
  <text x="930" y="1290" class="small-text" fill="white">• 状态同步: 更新主executor状态</text>
  
  <!-- 第五阶段：响应处理与用户反馈 -->
  <text x="50" y="1340" class="subtitle">阶段5: 响应处理与用户反馈</text>
  
  <rect x="50" y="1360" width="1650" height="200" class="tool-box"/>
  <text x="875" y="1385" text-anchor="middle" class="header-text">BaseAgentResponseHandler - 响应处理机制</text>
  
  <text x="70" y="1410" class="text" fill="white">📡 事件类型</text>
  <text x="80" y="1425" class="small-text" fill="white">• "plan": 计划生成/更新事件，包含完整任务列表和状态</text>
  <text x="80" y="1440" class="small-text" fill="white">• "task": 单个任务执行事件，包含任务内容和执行结果</text>
  <text x="80" y="1455" class="small-text" fill="white">• "plan_thought": 规划思考过程，500字以内reasoning</text>
  
  <text x="70" y="1475" class="text" fill="white">🔄 消息流转</text>
  <text x="80" y="1490" class="small-text" fill="white">• SSE流式推送: 实时向前端推送执行进度</text>
  <text x="80" y="1505" class="small-text" fill="white">• 消息排序: messageOrder确保顺序</text>
  <text x="80" y="1520" class="small-text" fill="white">• 任务关联: taskId关联子任务与主计划</text>
  
  <text x="70" y="1540" class="text" fill="white">✅ 完成标识</text>
  <text x="80" y="1555" class="small-text" fill="white">• isFinal判断: 确定是否为最终结果</text>
  
  <!-- 流程箭头 -->
  <line x1="400" y1="290" x2="675" y2="350" class="flow-arrow"/>
  <text x="500" y="325" class="small-text">任务列表传递</text>
  
  <line x1="675" y1="600" x2="350" y2="660" class="flow-arrow"/>
  <text x="450" y="635" class="small-text">单任务执行</text>
  
  <line x1="675" y1="600" x2="1000" y2="660" class="parallel-arrow"/>
  <text x="800" y="635" class="small-text">多任务并行</text>
  
  <line x1="450" y1="940" x2="450" y2="1000" class="state-arrow"/>
  <text x="460" y="970" class="small-text">状态管理</text>
  
  <line x1="1300" y1="940" x2="1300" y2="1000" class="parallel-arrow"/>
  <text x="1310" y="970" class="small-text">并发控制</text>
  
  <line x1="875" y1="1300" x2="875" y2="1360" class="flow-arrow"/>
  <text x="885" y="1330" class="small-text">响应处理</text>
  
  <!-- 总结框 -->
  <rect x="50" y="1600" width="1650" height="150" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="8"/>
  <text x="875" y="1625" text-anchor="middle" class="subtitle">执行流程总结</text>
  
  <text x="70" y="1650" class="text">🔄 <tspan class="subtitle">完整执行链路</tspan>：</text>
  <text x="80" y="1670" class="small-text">1. Planning Agent通过genie_sop_prompt拆分任务 → 2. PlanSolveHandler分发任务(单任务/并行) → 3. ExecutorAgent执行子任务</text>
  <text x="80" y="1685" class="small-text">4. 状态管理跟踪进度 → 5. 响应处理推送结果 → 6. 所有任务完成后返回"finish"</text>
  
  <text x="70" y="1710" class="text">⚡ <tspan class="subtitle">关键特性</tspan>：</text>
  <text x="80" y="1730" class="small-text">• 智能拆分(最多5个) • 并行执行(多任务) • 状态跟踪(not_started→in_progress→completed) • 数字员工(工具人格化) • SSE实时推送</text>
</svg>
