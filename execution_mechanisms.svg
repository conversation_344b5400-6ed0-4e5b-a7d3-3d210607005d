<svg width="2000" height="2400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .header-text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: white; }
      .html-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .code-box { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .search-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 3; rx: 10; }
      .tool-box { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .engine-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .flow-arrow { stroke: #34495e; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .parallel-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="1000" y="30" text-anchor="middle" class="title">JoyAgent-JDGenie 三大核心执行机制</text>
  
  <!-- 第一部分：HTML生成机制 -->
  <text x="50" y="70" class="subtitle">🌐 HTML生成机制</text>
  
  <!-- HTML Report工具 -->
  <rect x="50" y="90" width="600" height="320" class="html-box"/>
  <text x="350" y="115" text-anchor="middle" class="header-text">HTML Report 生成流程</text>
  
  <text x="70" y="140" class="text" fill="white">📋 文件分类处理</text>
  <text x="80" y="155" class="small-text" fill="white">• 代码输出文件: "代码输出" → key_files (优先级高)</text>
  <text x="80" y="170" class="small-text" fill="white">• 搜索结果文件: "_search_result.txt" → flatten_search_file()</text>
  <text x="80" y="185" class="small-text" fill="white">• 其他文件: md/txt/csv → flat_files</text>
  
  <text x="70" y="205" class="text" fill="white">🔧 内容处理</text>
  <text x="80" y="220" class="small-text" fill="white">• Token限制: 模型上下文长度 * 0.8</text>
  <text x="80" y="235" class="small-text" fill="white">• 文件截断: truncate_files() 保证不超限</text>
  <text x="80" y="250" class="small-text" fill="white">• 链接保留: 搜索结果保留原始链接</text>
  
  <text x="70" y="270" class="text" fill="white">🎨 模板渲染</text>
  <text x="80" y="285" class="small-text" fill="white">• Jinja2模板: html_task模板</text>
  <text x="80" y="300" class="small-text" fill="white">• 变量注入: task, key_files, files, date</text>
  <text x="80" y="315" class="small-text" fill="white">• LLM生成: 基于模板生成完整HTML</text>
  
  <text x="70" y="335" class="text" fill="white">💾 文件保存</text>
  <text x="80" y="350" class="small-text" fill="white">• 上传文件: upload_file() 保存到文件系统</text>
  <text x="80" y="365" class="small-text" fill="white">• 返回信息: fileInfo包含文件路径和元数据</text>
  <text x="80" y="380" class="small-text" fill="white">• 流式输出: SSE实时推送生成进度</text>
  
  <!-- HTML模板系统 -->
  <rect x="700" y="90" width="600" height="320" class="html-box"/>
  <text x="1000" y="115" text-anchor="middle" class="header-text">HTML模板系统</text>
  
  <text x="720" y="140" class="text" fill="white">📝 模板结构</text>
  <text x="730" y="155" class="small-text" fill="white">• html_task: 主要HTML生成模板</text>
  <text x="730" y="170" class="small-text" fill="white">• 响应式设计: 支持移动端和桌面端</text>
  <text x="730" y="185" class="small-text" fill="white">• 样式内嵌: CSS样式直接嵌入HTML</text>
  
  <text x="720" y="205" class="text" fill="white">🎯 内容组织</text>
  <text x="730" y="220" class="small-text" fill="white">• 标题区域: 任务描述和生成时间</text>
  <text x="730" y="235" class="small-text" fill="white">• 核心内容: key_files优先展示</text>
  <text x="730" y="250" class="small-text" fill="white">• 参考资料: flat_files作为支撑材料</text>
  <text x="730" y="265" class="small-text" fill="white">• 链接引用: 外部链接可点击跳转</text>
  
  <text x="720" y="285" class="text" fill="white">🔗 交互功能</text>
  <text x="730" y="300" class="small-text" fill="white">• 锚点导航: 页面内快速跳转</text>
  <text x="730" y="315" class="small-text" fill="white">• 折叠展开: 长内容支持折叠</text>
  <text x="730" y="330" class="small-text" fill="white">• 搜索高亮: 关键词高亮显示</text>
  
  <text x="720" y="350" class="text" fill="white">📊 数据可视化</text>
  <text x="730" y="365" class="small-text" fill="white">• 表格渲染: CSV数据自动转表格</text>
  <text x="730" y="380" class="small-text" fill="white">• 图表支持: 支持嵌入图表和图片</text>
  <text x="730" y="395" class="small-text" fill="white">• 代码高亮: Python代码语法高亮</text>
  
  <!-- 第二部分：代码执行机制 -->
  <text x="50" y="450" class="subtitle">🐍 Python代码执行机制</text>
  
  <!-- Code Interpreter Agent -->
  <rect x="50" y="470" width="650" height="380" class="code-box"/>
  <text x="375" y="495" text-anchor="middle" class="header-text">Code Interpreter Agent 执行流程</text>
  
  <text x="70" y="520" class="text" fill="white">🏗️ 环境准备</text>
  <text x="80" y="535" class="small-text" fill="white">• 临时目录: tempfile.mkdtemp() 创建隔离环境</text>
  <text x="80" y="550" class="small-text" fill="white">• 输出目录: output/ 子目录存放生成文件</text>
  <text x="80" y="565" class="small-text" fill="white">• 文件下载: download_all_files_in_path() 获取输入文件</text>
  
  <text x="70" y="585" class="text" fill="white">🤖 Agent创建</text>
  <text x="80" y="600" class="small-text" fill="white">• LiteLLMModel: 使用CODE_INTEPRETER_MODEL (默认gpt-4.1)</text>
  <text x="80" y="615" class="small-text" fill="white">• PythonInterpreterTool: 核心Python执行工具</text>
  <text x="80" y="630" class="small-text" fill="white">• 授权库: pandas, openpyxl, numpy, matplotlib, seaborn</text>
  
  <text x="70" y="650" class="text" fill="white">🔄 执行循环</text>
  <text x="80" y="665" class="small-text" fill="white">• Think: 分析任务，制定代码策略</text>
  <text x="80" y="680" class="small-text" fill="white">• Code: 生成Python代码，解析代码块</text>
  <text x="80" y="695" class="small-text" fill="white">• Execute: python_executor() 执行代码</text>
  <text x="80" y="710" class="small-text" fill="white">• Observe: 收集执行日志和输出结果</text>
  
  <text x="70" y="730" class="text" fill="white">⚡ 代码解析</text>
  <text x="80" y="745" class="small-text" fill="white">• parse_code_blobs(): 提取```python```代码块</text>
  <text x="80" y="760" class="small-text" fill="white">• fix_final_answer_code(): 修复代码格式问题</text>
  <text x="80" y="775" class="small-text" fill="white">• 变量持久化: 代码执行间变量和模块保持</text>
  
  <text x="70" y="795" class="text" fill="white">📤 结果处理</text>
  <text x="80" y="810" class="small-text" fill="white">• 流式输出: 实时推送执行进度</text>
  <text x="80" y="825" class="small-text" fill="white">• 文件上传: 生成的文件自动上传保存</text>
  <text x="80" y="840" class="small-text" fill="white">• 清理环境: 执行完成后清理临时目录</text>
  
  <!-- Python执行器详细机制 -->
  <rect x="750" y="470" width="650" height="380" class="code-box"/>
  <text x="1075" y="495" text-anchor="middle" class="header-text">Python执行器 (PythonInterpreterTool)</text>
  
  <text x="770" y="520" class="text" fill="white">🔒 安全沙箱</text>
  <text x="780" y="535" class="small-text" fill="white">• 隔离执行: 每个请求独立的Python环境</text>
  <text x="780" y="550" class="small-text" fill="white">• 库限制: 只允许预授权的安全库</text>
  <text x="780" y="565" class="small-text" fill="white">• 文件系统: 限制在指定目录内操作</text>
  
  <text x="770" y="585" class="text" fill="white">📊 数据处理</text>
  <text x="780" y="600" class="small-text" fill="white">• Pandas: Excel/CSV文件读写，使用openpyxl引擎</text>
  <text x="780" y="615" class="small-text" fill="white">• NumPy: 数值计算和数组操作</text>
  <text x="780" y="630" class="small-text" fill="white">• Matplotlib/Seaborn: 数据可视化和图表生成</text>
  
  <text x="770" y="650" class="text" fill="white">🎯 执行策略</text>
  <text x="780" y="665" class="small-text" fill="white">• 步骤限制: max_steps=10 防止无限循环</text>
  <text x="780" y="680" class="small-text" fill="white">• 错误处理: 捕获异常并提供详细错误信息</text>
  <text x="780" y="695" class="small-text" fill="white">• 输出控制: print()输出重定向到观察结果</text>
  
  <text x="770" y="715" class="text" fill="white">📝 代码规范</text>
  <text x="780" y="730" class="small-text" fill="white">• 中文思考: 思考过程使用中文</text>
  <text x="780" y="745" class="small-text" fill="white">• 英文代码: Python代码使用英文</text>
  <text x="780" y="760" class="small-text" fill="white">• 文件命名: 输出文件使用中文名称</text>
  
  <text x="770" y="780" class="text" fill="white">🔄 状态管理</text>
  <text x="780" y="795" class="small-text" fill="white">• 内存持久: 变量在多次执行间保持</text>
  <text x="780" y="810" class="small-text" fill="white">• 模块缓存: 导入的模块保持可用</text>
  <text x="780" y="825" class="small-text" fill="white">• 工作目录: 保持在指定的工作目录</text>
  
  <!-- 第三部分：网络搜索机制 -->
  <text x="50" y="890" class="subtitle">🔍 网络搜索机制</text>
  
  <!-- Deep Search系统 -->
  <rect x="50" y="910" width="650" height="400" class="search-box"/>
  <text x="375" y="935" text-anchor="middle" class="header-text">Deep Search 深度搜索系统</text>
  
  <text x="70" y="960" class="text" fill="white">🎯 查询分解</text>
  <text x="80" y="975" class="small-text" fill="white">• query_decompose(): 将复杂查询拆分为子查询</text>
  <text x="80" y="990" class="small-text" fill="white">• 多意图识别: 识别查询中的不同信息需求</text>
  <text x="80" y="1005" class="small-text" fill="white">• 去重机制: 避免重复搜索相同内容</text>
  
  <text x="70" y="1025" class="text" fill="white">🔄 循环搜索</text>
  <text x="80" y="1040" class="small-text" fill="white">• 最大轮数: max_loop控制搜索深度</text>
  <text x="80" y="1055" class="small-text" fill="white">• 增量搜索: 每轮基于前轮结果优化查询</text>
  <text x="80" y="1070" class="small-text" fill="white">• 完整性评估: 判断信息是否足够完整</text>
  
  <text x="70" y="1090" class="text" fill="white">📊 结果处理</text>
  <text x="80" y="1105" class="small-text" fill="white">• 去重合并: search_and_dedup() 去除重复结果</text>
  <text x="80" y="1120" class="small-text" fill="white">• 相关性排序: 按相关性对结果排序</text>
  <text x="80" y="1135" class="small-text" fill="white">• 内容截断: 控制单个结果的长度</text>
  
  <text x="70" y="1155" class="text" fill="white">📝 报告生成</text>
  <text x="80" y="1170" class="small-text" fill="white">• answer_prompt: 基于搜索结果生成详细报告</text>
  <text x="80" y="1185" class="small-text" fill="white">• 来源标注: 关键结论提供markdown引用链接</text>
  <text x="80" y="1200" class="small-text" fill="white">• 金字塔原理: 结构化组织信息</text>
  
  <text x="70" y="1220" class="text" fill="white">🔄 流式输出</text>
  <text x="80" y="1235" class="small-text" fill="white">• 实时推送: SSE流式推送搜索进度</text>
  <text x="80" y="1250" class="small-text" fill="white">• 分阶段输出: 查询分解→搜索→结果→报告</text>
  <text x="80" y="1265" class="small-text" fill="white">• 状态标识: isFinal标识最终结果</text>
  
  <text x="70" y="1285" class="text" fill="white">💾 结果保存</text>
  <text x="80" y="1300" class="small-text" fill="white">• 搜索文件: _search_result.txt格式保存</text>
  
  <!-- 多引擎搜索系统 -->
  <rect x="750" y="910" width="650" height="400" class="search-box"/>
  <text x="1075" y="935" text-anchor="middle" class="header-text">MixSearch 多引擎搜索系统</text>
  
  <text x="770" y="960" class="text" fill="white">🌐 搜索引擎</text>
  <text x="780" y="975" class="small-text" fill="white">• BingSearch: 微软Bing搜索API</text>
  <text x="780" y="990" class="small-text" fill="white">• JinaSearch: Jina搜索引擎</text>
  <text x="780" y="1005" class="small-text" fill="white">• SogouSearch: 搜狗搜索引擎</text>
  <text x="780" y="1020" class="small-text" fill="white">• SerperSearch: Serper搜索API</text>
  
  <text x="770" y="1040" class="text" fill="white">⚡ 并行搜索</text>
  <text x="780" y="1055" class="small-text" fill="white">• asyncio.TaskGroup: 并发执行多个搜索引擎</text>
  <text x="780" y="1070" class="small-text" fill="white">• 超时控制: 每个引擎独立超时设置</text>
  <text x="780" y="1085" class="small-text" fill="white">• 错误隔离: 单个引擎失败不影响其他</text>
  
  <text x="770" y="1105" class="text" fill="white">🔧 配置管理</text>
  <text x="780" y="1120" class="small-text" fill="white">• 环境变量: USE_SEARCH_ENGINE配置启用引擎</text>
  <text x="780" y="1135" class="small-text" fill="white">• API密钥: 各引擎独立的API密钥管理</text>
  <text x="780" y="1150" class="small-text" fill="white">• 网关模式: 支持JD内部网关和直连模式</text>
  
  <text x="770" y="1170" class="text" fill="white">📊 结果标准化</text>
  <text x="780" y="1185" class="small-text" fill="white">• Doc对象: 统一的搜索结果数据结构</text>
  <text x="780" y="1200" class="small-text" fill="white">• 字段映射: title, content, link, search_engine</text>
  <text x="780" y="1215" class="small-text" fill="white">• 去重算法: 基于内容相似度去重</text>
  
  <text x="770" y="1235" class="text" fill="white">🎯 质量控制</text>
  <text x="780" y="1250" class="small-text" fill="white">• 内容过滤: 过滤低质量和重复内容</text>
  <text x="780" y="1265" class="small-text" fill="white">• 相关性评分: 基于查询相关性排序</text>
  <text x="780" y="1280" class="small-text" fill="white">• 数量控制: 每个引擎返回结果数量限制</text>
  
  <!-- 第四部分：工具集成机制 -->
  <text x="50" y="1350" class="subtitle">🔧 工具集成与调用机制</text>
  
  <!-- Java后端工具调用 -->
  <rect x="50" y="1370" width="650" height="300" class="tool-box"/>
  <text x="375" y="1395" text-anchor="middle" class="header-text">Java后端工具调用机制</text>
  
  <text x="70" y="1420" class="text" fill="white">🎯 工具路由</text>
  <text x="80" y="1435" class="small-text" fill="white">• CodeInterpreterTool: 调用Python代码执行服务</text>
  <text x="80" y="1450" class="small-text" fill="white">• DeepSearchTool: 调用深度搜索服务</text>
  <text x="80" y="1465" class="small-text" fill="white">• ReportTool: 调用报告生成服务</text>
  
  <text x="70" y="1485" class="text" fill="white">📡 HTTP调用</text>
  <text x="80" y="1500" class="small-text" fill="white">• RESTful API: 通过HTTP调用Python服务</text>
  <text x="80" y="1515" class="small-text" fill="white">• 流式传输: SSE支持实时数据流</text>
  <text x="80" y="1530" class="small-text" fill="white">• 异步处理: Future异步获取结果</text>
  
  <text x="70" y="1550" class="text" fill="white">🔄 参数传递</text>
  <text x="80" y="1565" class="small-text" fill="white">• 请求构建: 将Agent参数转换为API请求</text>
  <text x="80" y="1580" class="small-text" fill="white">• 文件传递: 通过文件名列表传递文件引用</text>
  <text x="80" y="1595" class="small-text" fill="white">• 上下文传递: requestId, sessionId等上下文信息</text>
  
  <text x="70" y="1615" class="text" fill="white">📊 结果处理</text>
  <text x="80" y="1630" class="small-text" fill="white">• 响应解析: 解析JSON响应获取结果</text>
  <text x="80" y="1645" class="small-text" fill="white">• 文件管理: 处理生成的文件信息</text>
  <text x="80" y="1660" class="small-text" fill="white">• 错误处理: 捕获和处理工具执行异常</text>
  
  <!-- Python工具服务 -->
  <rect x="750" y="1370" width="650" height="300" class="tool-box"/>
  <text x="1075" y="1395" text-anchor="middle" class="header-text">Python工具服务架构</text>
  
  <text x="770" y="1420" class="text" fill="white">🌐 FastAPI服务</text>
  <text x="780" y="1435" class="small-text" fill="white">• /code_interpreter: 代码执行端点</text>
  <text x="780" y="1450" class="small-text" fill="white">• /report: 报告生成端点</text>
  <text x="780" y="1465" class="small-text" fill="white">• /deepsearch: 深度搜索端点</text>
  
  <text x="770" y="1485" class="text" fill="white">📡 流式响应</text>
  <text x="780" y="1500" class="small-text" fill="white">• EventSourceResponse: SSE流式响应</text>
  <text x="780" y="1515" class="small-text" fill="white">• ServerSentEvent: 标准SSE事件格式</text>
  <text x="780" y="1530" class="small-text" fill="white">• 心跳机制: 15秒心跳保持连接</text>
  
  <text x="770" y="1550" class="text" fill="white">📁 文件管理</text>
  <text x="780" y="1565" class="small-text" fill="white">• upload_file(): 文件上传和存储</text>
  <text x="780" y="1580" class="small-text" fill="white">• download_all_files(): 批量文件下载</text>
  <text x="780" y="1595" class="small-text" fill="white">• 临时目录: 隔离的工作环境</text>
  
  <text x="770" y="1615" class="text" fill="white">⚙️ 中间件</text>
  <text x="780" y="1630" class="small-text" fill="white">• RequestHandlerRoute: 请求处理路由</text>
  <text x="780" y="1645" class="small-text" fill="white">• 日志记录: 详细的执行日志</text>
  <text x="780" y="1660" class="small-text" fill="white">• 性能监控: timer装饰器监控性能</text>
  
  <!-- 流程箭头 -->
  <line x1="350" y1="410" x2="375" y2="470" class="flow-arrow"/>
  <text x="320" y="445" class="small-text">HTML生成</text>
  
  <line x1="375" y1="850" x2="375" y2="910" class="flow-arrow"/>
  <text x="385" y="885" class="small-text">搜索调用</text>
  
  <line x1="700" y1="1000" x2="750" y2="1000" class="parallel-arrow"/>
  <text x="710" y="995" class="small-text">多引擎</text>
  
  <line x1="375" y1="1310" x2="375" y2="1370" class="flow-arrow"/>
  <text x="385" y="1345" class="small-text">工具集成</text>
  
  <line x1="700" y1="1520" x2="750" y2="1520" class="flow-arrow"/>
  <text x="710" y="1515" class="small-text">服务调用</text>
  
  <!-- 总结框 -->
  <rect x="50" y="1700" width="1850" height="200" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="8"/>
  <text x="975" y="1725" text-anchor="middle" class="subtitle">三大执行机制总结</text>
  
  <text x="70" y="1750" class="text">🌐 <tspan class="subtitle">HTML生成</tspan>：</text>
  <text x="80" y="1770" class="small-text">文件分类处理 → 内容截断优化 → Jinja2模板渲染 → LLM生成HTML → 文件保存上传 → 响应式页面输出</text>
  
  <text x="70" y="1790" class="text">🐍 <tspan class="subtitle">代码执行</tspan>：</text>
  <text x="80" y="1810" class="small-text">隔离环境创建 → CIAgent初始化 → Think-Code-Execute-Observe循环 → Python沙箱执行 → 结果文件生成 → 流式进度推送</text>
  
  <text x="70" y="1830" class="text">🔍 <tspan class="subtitle">网络搜索</tspan>：</text>
  <text x="80" y="1850" class="small-text">查询分解 → 多引擎并行搜索(Bing/Jina/Sogou/Serper) → 结果去重合并 → 深度循环搜索 → 智能报告生成 → 来源链接标注</text>
  
  <text x="70" y="1870" class="text">🔧 <tspan class="subtitle">集成机制</tspan>：Java后端通过HTTP调用Python服务，SSE流式传输，异步处理，文件系统统一管理</text>
</svg>
