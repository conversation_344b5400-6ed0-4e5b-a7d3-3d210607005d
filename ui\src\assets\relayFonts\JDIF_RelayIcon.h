
  //
  // JDIF_RelayIcon.h
  // fontFamily:RelayIcon
  //
  // Version 1.0
  // Copyright (C) 2019 by original authors @ master Gao
  //

  #ifndef JDIF_RelayIcon_H
  #define JDIF_RelayIcon_H

  NSString * const JDIF_RelayIcon = @"RelayIcon";

  NSString * const JDIF_ICON_TIAOSHI = @"\U0000f3a2"; // 调试
  NSString * const JDIF_ICON_SOUSUOQUDAO = @"\U0000f31d"; // 搜索渠道
  NSString * const JDIF_ICON_CHAJIAN = @"\U0000eec3"; // 插件
  NSString * const JDIF_ICON_RENWU = @"\U0000eb95"; // 任务
  NSString * const JDIF_ICON_SHANGJIANTOU = @"\U0000f189"; // 上箭头
  NSString * const JDIF_ICON_HUANYUAN = @"\U0000e963"; // 还原
  NSString * const JDIF_ICON_MOXINGSHANGDIAN = @"\U0000f117"; // 模型商店
  NSString * const JDIF_ICON_GENGDUO = @"\U0000e388"; // 更多
  NSString * const JDIF_ICON_MOXINGJINGTIAOTIANCHONG = @"\U0000e5a2"; // 模型精调（填充）
  NSString * const JDIF_ICON_SHUJUYUANTIANCHONG = @"\U0000e52e"; // 数据源（填充）
  NSString * const JDIF_ICON_BOTSHANGDIAN = @"\U0000e5e9"; // bot商店
  NSString * const JDIF_ICON_ZHINENGTI = @"\U0000f450"; // 智能体
  NSString * const JDIF_ICON_YONGHU = @"\U0000f15f"; // 用户
  NSString * const JDIF_ICON_BAOCUN = @"\U0000f0d6"; // 保存
  NSString * const JDIF_ICON_QUDAOSOUSUO = @"\U0000e088"; // 渠道搜索
  NSString * const JDIF_ICON_GONGZUOLIU = @"\U0000e79c"; // 工作流
  NSString * const JDIF_ICON_SHIPINTIANCHONG = @"\U0000e32e"; // 视频(填充)
  NSString * const JDIF_ICON_FUJIAN = @"\U0000e3e2"; // 附件
  NSString * const JDIF_ICON_YOUJIANTOU = @"\U0000eca0"; // 右
  NSString * const JDIF_ICON_YICHANGBUCHULI = @"\U0000f12a"; // 异常不处理
  NSString * const JDIF_ICON_ZANTINGTIANCHONGLAN = @"\U0000e711"; // 暂停（填充蓝）
  NSString * const JDIF_ICON_RIZHI = @"\U0000e975"; // 日志
  NSString * const JDIF_ICON_SHIJIAN1 = @"\U0000e508"; // 时间
  NSString * const JDIF_ICON_XIHUAN = @"\U0000e509"; // 喜欢
  NSString * const JDIF_ICON_SHOUCANG_TIANCHONG = @"\U0000ea45"; // 收藏(填充)
  NSString * const JDIF_ICON_QINGCHU = @"\U0000e8a1"; // 清除
  NSString * const JDIF_ICON_YUYIN = @"\U0000ec16"; // 语音
  NSString * const JDIF_ICON_BOFANG = @"\U0000e776"; // 播放
  NSString * const JDIF_ICON_FASONG = @"\U0000f1a8"; // 发送
  NSString * const JDIF_ICON_BUDIANZANTIANCHONG = @"\U0000e2d5"; // 不点赞（填充）
  NSString * const JDIF_ICON_SHEZHI = @"\U0000ecdd"; // 设置
  NSString * const JDIF_ICON_WODEKONGJIANTIANCHONG = @"\U0000e4eb"; // 我的空间（填充）
  NSString * const JDIF_ICON_GENGDUOJINENG = @"\U0000f4d1"; // 更多技能
  NSString * const JDIF_ICON_PAIXU = @"\U0000eb3d"; // 排序
  NSString * const JDIF_ICON_CHAKANSHILI = @"\U0000ecda"; // 查看示例
  NSString * const JDIF_ICON_SHOUCANG = @"\U0000e32b"; // 收藏
  NSString * const JDIF_ICON_MCP = @"\U0000ec80"; // MCP
  NSString * const JDIF_ICON_PPT = @"\U0000f322"; // PPT
  NSString * const JDIF_ICON_QUDAOSOUSUOTIANCHONG = @"\U0000e863"; // 渠道搜索（填充）
  NSString * const JDIF_ICON_ZHANKAISHANGXIA = @"\U0000e48c"; // 展开 (上下)
  NSString * const JDIF_ICON_RENWUZHONGXINTIANCHONG3 = @"\U0000ecd0"; // 任务中心（填充）
  NSString * const JDIF_ICON_DONGDONG1 = @"\U0000ecec"; // 咚咚
  NSString * const JDIF_ICON_TIXING = @"\U0000e31d"; // 提醒
  NSString * const JDIF_ICON_RIZHITIANCHONG = @"\U0000f1e8"; // 日志（填充）
  NSString * const JDIF_ICON_DONGDONG = @"\U0000e152"; // 咚咚
  NSString * const JDIF_ICON_SHOUQISHANGXIA = @"\U0000f2ff"; // 收起 (上下)
  NSString * const JDIF_ICON_SHUJUKUTIANCHONG = @"\U0000e6a5"; // 数据库（填充）
  NSString * const JDIF_ICON_SHIYONGLIANG = @"\U0000f025"; // 使用量
  NSString * const JDIF_ICON_DIANZANTIANCHONG = @"\U0000f2a1"; // 点赞(填充)
  NSString * const JDIF_ICON_XIANGSHANGTIANJIA = @"\U0000e36f"; // 向上添加
  NSString * const JDIF_ICON_XIANGXIATIANJIA = @"\U0000e0de"; // 向下添加
  NSString * const JDIF_ICON_YUECHI = @"\U0000e006"; // 钥匙
  NSString * const JDIF_ICON_DUIGOU = @"\U0000e6ca"; // 对勾
  NSString * const JDIF_ICON_JIESUO1 = @"\U0000f0b9"; // 解锁
  NSString * const JDIF_ICON_ZHINENGTITIANCHONG = @"\U0000ede1"; // 智能体（填充）
  NSString * const JDIF_ICON_WEIJIESUO2 = @"\U0000ed19"; // 未解锁
  NSString * const JDIF_ICON_WEIWANCHENG = @"\U0000e263"; // 未完成
  NSString * const JDIF_ICON_BUZANCHENG = @"\U0000ea9a"; // 不赞成
  NSString * const JDIF_ICON_WODEKONGJIAN = @"\U0000f0f1"; // 我的空间
  NSString * const JDIF_ICON_FANHUISHANGYIBU = @"\U0000eb13"; // 返回上一步
  NSString * const JDIF_ICON_JULI = @"\U0000e5d6"; // 举例
  NSString * const JDIF_ICON_GENIE = @"\U0000e771"; // genie
  NSString * const JDIF_ICON_ZHANKAI = @"\U0000eaba"; // 展开
  NSString * const JDIF_ICON_GENGDUOICON = @"\U0000f394"; // 更多hover
  NSString * const JDIF_ICON_ZHONGMINGMING = @"\U0000f3ae"; // 重命名
  NSString * const JDIF_ICON_LIANWANGSOUSUO = @"\U0000e011"; // 联网搜索
  NSString * const JDIF_ICON_GUANFANG = @"\U0000ec72"; // 官方
  NSString * const JDIF_ICON_SHANCHU = @"\U0000ef5a"; // 删除
  NSString * const JDIF_ICON_BIANLIANG = @"\U0000e533"; // 变量
  NSString * const JDIF_ICON_KAPIANTIANCHONG = @"\U0000e4e2"; // 卡片（填充）
  NSString * const JDIF_ICON_ZHONGMINGMING_1 = @"\U0000f055"; // 重命名
  NSString * const JDIF_ICON_SHENDUSIKAO = @"\U0000e9b6"; // 深度思考
  NSString * const JDIF_ICON_SHUJUYUAN = @"\U0000f097"; // 数据源
  NSString * const JDIF_ICON_MCPTIANCHONG = @"\U0000f478"; // MCP（填充）
  NSString * const JDIF_ICON_JIA_1 = @"\U0000eed0"; // 清除
  NSString * const JDIF_ICON_CHAJIANTIANCHONG = @"\U0000f4c4"; // 插件（填充）
  NSString * const JDIF_ICON_BIAOGE = @"\U0000e9bc"; // 表格
  NSString * const JDIF_ICON_SHAIXUAN1 = @"\U0000ea70"; // 筛选
  NSString * const JDIF_ICON_GUANBI = @"\U0000e1a2"; // 关闭
  NSString * const JDIF_ICON_XINJIANZHENGYUAN = @"\U0000f03a"; // 新建正圆
  NSString * const JDIF_ICON_TUPIAN = @"\U0000e144"; // 图片
  NSString * const JDIF_ICON_WENDANG = @"\U0000e5b8"; // 文档
  NSString * const JDIF_ICON_SHOUQI = @"\U0000e4e6"; // 收起
  NSString * const JDIF_ICON_FENXIANG = @"\U0000e158"; // 分享
  NSString * const JDIF_ICON_TUPIANTIANCHONG = @"\U0000e341"; // 图片(填充)
  NSString * const JDIF_ICON_RIQIICON = @"\U0000e8d7"; // 日期
  NSString * const JDIF_ICON_WENJIAN = @"\U0000f401"; // 文件
  NSString * const JDIF_ICON_FASONGTIANCHONG = @"\U0000e3e7"; // 发送(填充)
  NSString * const JDIF_ICON_ZHISHIKUTIANCHONG = @"\U0000e6c3"; // 知识库（填充）
  NSString * const JDIF_ICON_SHUJUKU = @"\U0000e5af"; // 数据库
  NSString * const JDIF_ICON_RENWUZHONGXIN = @"\U0000f0c3"; // 任务中心
  NSString * const JDIF_ICON_TUANDUIKONGJIAN = @"\U0000e2c9"; // 团队空间
  NSString * const JDIF_ICON_MOXINGJINGTIAO = @"\U0000edcf"; // 模型精调
  NSString * const JDIF_ICON_XINJIANJIANTOU = @"\U0000edc9"; // 前往箭头
  NSString * const JDIF_ICON_DAIMA = @"\U0000f09c"; // 代码
  NSString * const JDIF_ICON_YINYONG = @"\U0000f32b"; // 引用
  NSString * const JDIF_ICON_CHAJIANSHANGDIAN = @"\U0000eef8"; // 插件商店
  NSString * const JDIF_ICON_GENGDUOICON_11 = @"\U0000efc5"; // 更多-1
  NSString * const JDIF_ICON_SHANGCHUAN = @"\U0000e3bc"; // 上传
  NSString * const JDIF_ICON_LIANJIE = @"\U0000e968"; // 链接
  NSString * const JDIF_ICON_XIAOXI = @"\U0000ed71"; // 消息
  NSString * const JDIF_ICON_XIAZAI = @"\U0000f503"; // 下载
  NSString * const JDIF_ICON_BIANJI = @"\U0000f349"; // 编辑
  NSString * const JDIF_ICON_YICHANG = @"\U0000eb88"; // 异常
  NSString * const JDIF_ICON_KAPIAN = @"\U0000ec20"; // 卡片
  NSString * const JDIF_ICON_DIANZAN = @"\U0000e9e1"; // 点赞
  NSString * const JDIF_ICON_YIWANCHENG = @"\U0000edee"; // 已完成
  NSString * const JDIF_ICON_SHIPIN = @"\U0000e0d8"; // 视频
  NSString * const JDIF_ICON_GONGZUOLIUTIANCHONG = @"\U0000e9ab"; // 工作流（填充）
  NSString * const JDIF_ICON_JINGSHI = @"\U0000e3d1"; // 警示
  NSString * const JDIF_ICON_CHAJIANSHANGDIANTIANCHONG = @"\U0000eb63"; // 插件商店（填充）
  NSString * const JDIF_ICON_ZHINENGTIANCHONG = @"\U0000eb92"; // 智能填充
  NSString * const JDIF_ICON_ZANTING = @"\U0000e10a"; // 暂停
  NSString * const JDIF_ICON_XIAJIANTOU = @"\U0000e5ab"; // 下箭头
  NSString * const JDIF_ICON_FUZHI = @"\U0000e333"; // 复制
  NSString * const JDIF_ICON_TIANJIA = @"\U0000e372"; // 添加
  NSString * const JDIF_ICON_SHOUYE = @"\U0000e90c"; // 首页
  NSString * const JDIF_ICON_YIWANCHENGTIANCHONG = @"\U0000eccf"; // 已完成（填充）
  NSString * const JDIF_ICON_ZANTINGTIANCHONG = @"\U0000e35e"; // 暂停（填充）
  NSString * const JDIF_ICON_TIAOZHUAN1 = @"\U0000e452"; // 跳转
  NSString * const JDIF_ICON_QINGQIUKAIFANG = @"\U0000e47b"; // 请求开放
  NSString * const JDIF_ICON_XINJIANDUIHUA = @"\U0000ef17"; // 新建对话
  NSString * const JDIF_ICON_JIA = @"\U0000ee79"; // 加
  NSString * const JDIF_ICON_JINGTIAOSHUJUTIANCHONG1 = @"\U0000e6f4"; // 精调数据（填充）
  NSString * const JDIF_ICON_FANHUI = @"\U0000e3cf"; // 左
  NSString * const JDIF_ICON_ZHENGYAN = @"\U0000e579"; // 睁眼
  NSString * const JDIF_ICON_SHUJUKANBAN = @"\U0000eff3"; // 数据看板
  NSString * const JDIF_ICON_SHUAXIN = @"\U0000f374"; // 刷新
  NSString * const JDIF_ICON_DIANNAO = @"\U0000ea3f"; // 电脑
  NSString * const JDIF_ICON_SOUSUO = @"\U0000f466"; // 搜索
  NSString * const JDIF_ICON_ZHISHIKU = @"\U0000e571"; // 知识库
  NSString * const JDIF_ICON_BOTSHANGDIANTIANCHONG = @"\U0000e18b"; // bot商店（填充）
  NSString * const JDIF_ICON_TUOZHUAIPAIXU1 = @"\U0000ef7f"; // 拖拽排序
  NSString * const JDIF_ICON_ZHENGYAN_1 = @"\U0000e073"; // 闭眼
  NSString * const JDIF_ICON_JIAN = @"\U0000f2cc"; // 减
  NSString * const JDIF_ICON_YUYINTIANCHONG = @"\U0000eea0"; // 语音(填充)
  NSString * const JDIF_ICON_GENIETIANCHONG = @"\U0000f11b"; // genie（填充）
  NSString * const JDIF_ICON_MOXINGSHANGDIANTIANCHONG = @"\U0000e5ae"; // 模型商店（填充）
  NSString * const JDIF_ICON_XIHUAN_TIANCHONG = @"\U0000e1be"; // 喜欢(填充)
  NSString * const JDIF_ICON_JINGTIAOSHUJU1 = @"\U0000e649"; // 精调数据
  NSString * const JDIF_ICON_YIWEN = @"\U0000e1b6"; // 疑问

  #endif