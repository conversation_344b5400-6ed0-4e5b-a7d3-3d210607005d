<svg width="1800" height="1600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; }
      .header-text { font-family: Arial, sans-serif; font-size: 11px; font-weight: bold; fill: white; }
      .java-box { fill: #e74c3c; stroke: #c0392b; stroke-width: 3; rx: 10; }
      .python-tool-box { fill: #3498db; stroke: #2980b9; stroke-width: 3; rx: 10; }
      .python-client-box { fill: #2ecc71; stroke: #27ae60; stroke-width: 3; rx: 10; }
      .ui-box { fill: #f39c12; stroke: #e67e22; stroke-width: 3; rx: 10; }
      .external-box { fill: #9b59b6; stroke: #8e44ad; stroke-width: 3; rx: 10; }
      .flow-arrow { stroke: #34495e; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .http-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
      .sse-arrow { stroke: #2ecc71; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 10,5; }
      .mcp-arrow { stroke: #9b59b6; stroke-width: 2; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 3,3; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="900" y="30" text-anchor="middle" class="title">JoyAgent-JDGenie 多项目架构与联调机制</text>
  
  <!-- genie-backend (Java项目) -->
  <rect x="50" y="80" width="500" height="400" class="java-box"/>
  <text x="300" y="110" text-anchor="middle" class="header-text">genie-backend (Java + Spring Boot)</text>
  <text x="300" y="130" text-anchor="middle" class="header-text">端口: 8080 | 核心智能体引擎</text>
  
  <text x="70" y="160" class="text" fill="white">🧠 核心职责</text>
  <text x="80" y="175" class="small-text" fill="white">• 多Agent系统管理 (ReAct, Planning, Executor等)</text>
  <text x="80" y="190" class="small-text" fill="white">• LLM调用与Function Call处理</text>
  <text x="80" y="205" class="small-text" fill="white">• 工具集合管理 (ToolCollection)</text>
  <text x="80" y="220" class="small-text" fill="white">• SSE流式响应处理</text>
  <text x="80" y="235" class="small-text" fill="white">• 上下文管理与Memory维护</text>
  
  <text x="70" y="255" class="text" fill="white">🔧 主要组件</text>
  <text x="80" y="270" class="small-text" fill="white">• GenieController - 主要API入口</text>
  <text x="80" y="285" class="small-text" fill="white">• AgentHandlerFactory - Agent路由</text>
  <text x="80" y="300" class="small-text" fill="white">• LLM - 大模型调用封装</text>
  <text x="80" y="315" class="small-text" fill="white">• BaseTool接口 - 工具标准化</text>
  <text x="80" y="330" class="small-text" fill="white">• McpTool - MCP协议客户端</text>
  
  <text x="70" y="350" class="text" fill="white">📡 对外接口</text>
  <text x="80" y="365" class="small-text" fill="white">• POST /v1/agent/chat - 主要对话接口</text>
  <text x="80" y="380" class="small-text" fill="white">• SSE流式响应支持</text>
  <text x="80" y="395" class="small-text" fill="white">• 文件上传下载管理</text>
  
  <text x="70" y="415" class="text" fill="white">⚙️ 配置依赖</text>
  <text x="80" y="430" class="small-text" fill="white">• code_interpreter_url: http://127.0.0.1:1601</text>
  <text x="80" y="445" class="small-text" fill="white">• deep_search_url: http://127.0.0.1:1601</text>
  <text x="80" y="460" class="small-text" fill="white">• mcp_client_url: http://127.0.0.1:8188</text>
  
  <!-- genie-tool (Python项目) -->
  <rect x="600" y="80" width="500" height="400" class="python-tool-box"/>
  <text x="850" y="110" text-anchor="middle" class="header-text">genie-tool (Python + FastAPI)</text>
  <text x="850" y="130" text-anchor="middle" class="header-text">端口: 1601 | 工具执行引擎</text>
  
  <text x="620" y="160" class="text" fill="white">🛠️ 核心职责</text>
  <text x="630" y="175" class="small-text" fill="white">• 代码解释器 (Code Interpreter)</text>
  <text x="630" y="190" class="small-text" fill="white">• 深度搜索 (Deep Search)</text>
  <text x="630" y="205" class="small-text" fill="white">• 报告生成 (Report Generation)</text>
  <text x="630" y="220" class="small-text" fill="white">• 文件处理与管理</text>
  <text x="630" y="235" class="small-text" fill="white">• 多搜索引擎集成</text>
  
  <text x="620" y="255" class="text" fill="white">🔧 主要工具</text>
  <text x="630" y="270" class="small-text" fill="white">• code_interpreter_agent - Python代码执行</text>
  <text x="630" y="285" class="small-text" fill="white">• DeepSearch - 智能搜索与推理</text>
  <text x="630" y="300" class="small-text" fill="white">• report - Markdown/HTML/PPT生成</text>
  <text x="630" y="315" class="small-text" fill="white">• file_util - 文件截断与处理</text>
  <text x="630" y="330" class="small-text" fill="white">• MixSearch - 多引擎搜索聚合</text>
  
  <text x="620" y="350" class="text" fill="white">📡 API接口</text>
  <text x="630" y="365" class="small-text" fill="white">• POST /v1/tool/code_interpreter</text>
  <text x="630" y="380" class="small-text" fill="white">• POST /v1/tool/deep_search</text>
  <text x="630" y="395" class="small-text" fill="white">• POST /v1/tool/report</text>
  <text x="630" y="410" class="small-text" fill="white">• POST /v1/file_tool/* (文件管理)</text>
  
  <text x="620" y="430" class="text" fill="white">🔄 流式处理</text>
  <text x="630" y="445" class="small-text" fill="white">• SSE流式响应支持</text>
  <text x="630" y="460" class="small-text" fill="white">• 实时代码执行结果推送</text>
  
  <!-- genie-client (Python项目) -->
  <rect x="1150" y="80" width="500" height="400" class="python-client-box"/>
  <text x="1400" y="110" text-anchor="middle" class="header-text">genie-client (Python + FastAPI)</text>
  <text x="1400" y="130" text-anchor="middle" class="header-text">端口: 8188 | MCP协议代理</text>
  
  <text x="1170" y="160" class="text" fill="white">🌐 核心职责</text>
  <text x="1180" y="175" class="small-text" fill="white">• MCP协议客户端实现</text>
  <text x="1180" y="190" class="small-text" fill="white">• SSE连接管理</text>
  <text x="1180" y="205" class="small-text" fill="white">• 第三方MCP服务器代理</text>
  <text x="1180" y="220" class="small-text" fill="white">• 工具发现与调用</text>
  <text x="1180" y="235" class="small-text" fill="white">• 认证与头部管理</text>
  
  <text x="1170" y="255" class="text" fill="white">🔧 主要组件</text>
  <text x="1180" y="270" class="small-text" fill="white">• SseClient - SSE客户端封装</text>
  <text x="1180" y="285" class="small-text" fill="white">• HeaderEntity - 请求头管理</text>
  <text x="1180" y="300" class="small-text" fill="white">• ClientSession - MCP会话管理</text>
  <text x="1180" y="315" class="small-text" fill="white">• 连接池与超时控制</text>
  
  <text x="1170" y="335" class="text" fill="white">📡 API接口</text>
  <text x="1180" y="350" class="small-text" fill="white">• GET /health - 健康检查</text>
  <text x="1180" y="365" class="small-text" fill="white">• POST /v1/tool/list - 工具列表</text>
  <text x="1180" y="380" class="small-text" fill="white">• POST /v1/tool/call - 工具调用</text>
  <text x="1180" y="395" class="small-text" fill="white">• POST /v1/serv/pong - 服务器测试</text>
  
  <text x="1170" y="415" class="text" fill="white">🔗 支持协议</text>
  <text x="1180" y="430" class="small-text" fill="white">• MCP (Model Context Protocol)</text>
  <text x="1180" y="445" class="small-text" fill="white">• SSE (Server-Sent Events)</text>
  <text x="1180" y="460" class="small-text" fill="white">• HTTP/HTTPS代理转发</text>
  
  <!-- UI前端 -->
  <rect x="50" y="520" width="400" height="200" class="ui-box"/>
  <text x="250" y="545" text-anchor="middle" class="header-text">UI前端 (Vue3 + TypeScript)</text>
  <text x="250" y="565" text-anchor="middle" class="header-text">端口: 3000 | 用户交互界面</text>
  
  <text x="70" y="590" class="text" fill="white">🖥️ 核心功能</text>
  <text x="80" y="605" class="small-text" fill="white">• 对话界面与消息展示</text>
  <text x="80" y="620" class="small-text" fill="white">• 文件上传下载管理</text>
  <text x="80" y="635" class="small-text" fill="white">• SSE实时消息接收</text>
  <text x="80" y="650" class="small-text" fill="white">• Agent类型选择</text>
  
  <text x="70" y="670" class="text" fill="white">🔧 技术栈</text>
  <text x="80" y="685" class="small-text" fill="white">• Vue3 + Composition API</text>
  <text x="80" y="700" class="small-text" fill="white">• TypeScript + Vite</text>
  
  <!-- 外部服务 -->
  <rect x="500" y="520" width="400" height="200" class="external-box"/>
  <text x="700" y="545" text-anchor="middle" class="header-text">外部服务</text>
  
  <text x="520" y="570" class="text" fill="white">🤖 LLM服务</text>
  <text x="530" y="585" class="small-text" fill="white">• OpenAI API兼容接口</text>
  <text x="530" y="600" class="small-text" fill="white">• Claude、GPT等模型支持</text>
  
  <text x="520" y="620" class="text" fill="white">🔌 MCP服务器</text>
  <text x="530" y="635" class="small-text" fill="white">• 第三方工具服务</text>
  <text x="530" y="650" class="small-text" fill="white">• 12306、天气、地图等</text>
  
  <text x="520" y="670" class="text" fill="white">🔍 搜索引擎</text>
  <text x="530" y="685" class="small-text" fill="white">• Bing、Jina、Sogou、SERP</text>
  <text x="530" y="700" class="small-text" fill="white">• 并发搜索与结果聚合</text>
  
  <!-- 第三方MCP服务器 -->
  <rect x="950" y="520" width="400" height="200" class="external-box"/>
  <text x="1150" y="545" text-anchor="middle" class="header-text">第三方MCP服务器</text>
  
  <text x="970" y="570" class="text" fill="white">🚄 12306票务服务</text>
  <text x="980" y="585" class="small-text" fill="white">• 火车票查询与预订</text>
  
  <text x="970" y="605" class="text" fill="white">🌤️ 天气服务</text>
  <text x="980" y="620" class="small-text" fill="white">• 实时天气查询</text>
  
  <text x="970" y="640" class="text" fill="white">🗺️ 地图服务</text>
  <text x="980" y="655" class="small-text" fill="white">• 地理位置与导航</text>
  
  <text x="970" y="675" class="text" fill="white">📈 其他工具</text>
  <text x="980" y="690" class="small-text" fill="white">• 股票、新闻、翻译等</text>
  <text x="980" y="705" class="small-text" fill="white">• 支持SSE协议的任意服务</text>
  
  <!-- 联调流程说明 -->
  <rect x="50" y="750" width="1600" height="300" class="java-box"/>
  <text x="850" y="780" text-anchor="middle" class="header-text">联调机制与数据流</text>
  
  <text x="70" y="810" class="text" fill="white">1️⃣ 用户请求流程</text>
  <text x="80" y="825" class="small-text" fill="white">UI前端 → HTTP POST → genie-backend:8080/v1/agent/chat → Agent处理 → 工具调用决策</text>
  
  <text x="70" y="845" class="text" fill="white">2️⃣ 内置工具调用</text>
  <text x="80" y="860" class="small-text" fill="white">genie-backend → HTTP POST → genie-tool:1601/v1/tool/* → Python工具执行 → SSE流式返回</text>
  
  <text x="70" y="880" class="text" fill="white">3️⃣ MCP工具调用</text>
  <text x="80" y="895" class="small-text" fill="white">genie-backend → HTTP POST → genie-client:8188/v1/tool/call → SSE连接 → 第三方MCP服务器</text>
  
  <text x="70" y="915" class="text" fill="white">4️⃣ 响应返回流程</text>
  <text x="80" y="930" class="small-text" fill="white">工具结果 → genie-backend聚合 → SSE流式推送 → UI前端实时展示</text>
  
  <text x="70" y="950" class="text" fill="white">5️⃣ 关键配置参数</text>
  <text x="80" y="965" class="small-text" fill="white">• genie-backend application.yml: 配置各服务URL和端口</text>
  <text x="80" y="980" class="small-text" fill="white">• 超时设置: 连接60s, 读取1800s, SSE长连接支持</text>
  <text x="80" y="995" class="small-text" fill="white">• 错误处理: 服务不可用时优雅降级，日志记录</text>
  
  <text x="70" y="1015" class="text" fill="white">6️⃣ 部署与启动</text>
  <text x="80" y="1030" class="small-text" fill="white">• 一键启动: sh Genie_start.sh (启动所有服务)</text>
  <text x="80" y="1045" class="small-text" fill="white">• 依赖检查: sh check_dep_port.sh (检查端口占用)</text>
  
  <!-- 连接箭头 -->
  <!-- UI到Backend -->
  <line x1="250" y1="520" x2="300" y2="480" class="http-arrow"/>
  <text x="220" y="500" class="small-text">HTTP POST</text>
  <text x="220" y="515" class="small-text">SSE响应</text>
  
  <!-- Backend到Tool -->
  <line x1="550" y1="280" x2="600" y2="280" class="http-arrow"/>
  <text x="560" y="270" class="small-text">HTTP POST</text>
  <text x="560" y="295" class="small-text">1601端口</text>
  
  <!-- Backend到Client -->
  <line x1="550" y1="200" x2="1150" y2="200" class="mcp-arrow"/>
  <text x="800" y="190" class="small-text">MCP调用</text>
  <text x="800" y="215" class="small-text">8188端口</text>
  
  <!-- Client到External MCP -->
  <line x1="1400" y1="480" x2="1150" y2="520" class="sse-arrow"/>
  <text x="1250" y="500" class="small-text">SSE连接</text>
  
  <!-- Backend到External Services -->
  <line x1="300" y1="480" x2="700" y2="520" class="http-arrow"/>
  <text x="450" y="500" class="small-text">LLM调用</text>
  
  <!-- Tool到External Search -->
  <line x1="850" y1="480" x2="700" y2="520" class="http-arrow"/>
  <text x="750" y="500" class="small-text">搜索API</text>
  
  <!-- 图例 -->
  <rect x="1400" y="520" width="350" height="200" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="8"/>
  <text x="1575" y="545" text-anchor="middle" class="subtitle">图例说明</text>
  
  <line x1="1420" y1="565" x2="1460" y2="565" class="http-arrow"/>
  <text x="1470" y="570" class="small-text">HTTP请求 (虚线)</text>
  
  <line x1="1420" y1="585" x2="1460" y2="585" class="sse-arrow"/>
  <text x="1470" y="590" class="small-text">SSE连接 (点划线)</text>
  
  <line x1="1420" y1="605" x2="1460" y2="605" class="mcp-arrow"/>
  <text x="1470" y="610" class="small-text">MCP协议 (点线)</text>
  
  <rect x="1420" y="625" width="20" height="15" class="java-box"/>
  <text x="1450" y="635" class="small-text">Java项目</text>
  
  <rect x="1420" y="645" width="20" height="15" class="python-tool-box"/>
  <text x="1450" y="655" class="small-text">Python工具项目</text>
  
  <rect x="1420" y="665" width="20" height="15" class="python-client-box"/>
  <text x="1450" y="675" class="small-text">Python客户端项目</text>
  
  <rect x="1420" y="685" width="20" height="15" class="external-box"/>
  <text x="1450" y="695" class="small-text">外部服务</text>
</svg>
