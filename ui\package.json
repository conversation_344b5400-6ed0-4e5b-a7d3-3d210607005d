{"name": "genie-ui", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build --mode production", "lint": "eslint .", "fix": "eslint . --fix", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@microsoft/fetch-event-source": "^2.0.1", "ahooks": "^3.9.0", "antd": "^5.26.3", "axios": "^1.10.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "lodash": "^4.17.21", "mermaid": "^11.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-frame-component": "^5.2.7", "react-json-pretty": "^2.2.0", "react-lottie": "^1.2.10", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.2", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@rollup/rollup-linux-arm64-gnu": "^4.46.0", "@rollup/rollup-linux-x64-gnu": "^4.46.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.10", "@types/lodash": "^4.17.20", "@types/mockjs": "^1.0.10", "@types/node": "^24.0.10", "@types/papaparse": "^5.3.16", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-lottie": "^1.2.10", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "mockjs": "^1.1.0", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "terser": "^5.43.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "unique-commit-id": "^1.0.0", "vite": "^6.1.0", "vite-plugin-mock": "^3.0.2"}}